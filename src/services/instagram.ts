/**
 * Instagram API Integration Service
 *
 * This service handles authentication and posting to Instagram
 * using the Instagram Graph API.
 */

import axios from 'axios';
import { formatInstagramHashtags } from '../utils/instagram-validators';

// Instagram API configuration
const INSTAGRAM_API_VERSION = 'v18.0';
const INSTAGRAM_API_BASE_URL = `https://graph.facebook.com/${INSTAGRAM_API_VERSION}`;

// Instagram Graph API scopes needed for posting
export const INSTAGRAM_REQUIRED_SCOPES = [
  'instagram_basic',
  'instagram_content_publish',
  'instagram_manage_comments',
  'instagram_manage_insights',
  'pages_read_engagement',
  'pages_show_list',
];

/**
 * Get Instagram login URL for authentication
 * @param clientId Facebook App ID
 * @param redirectUri Redirect URI after authentication
 * @returns Login URL for Instagram authentication
 */
export const getInstagramLoginUrl = (clientId: string, redirectUri: string): string => {
  const scopes = INSTAGRAM_REQUIRED_SCOPES.join(',');
  return `https://www.facebook.com/${INSTAGRAM_API_VERSION}/dialog/oauth?client_id=${clientId}&redirect_uri=${encodeURIComponent(
    redirectUri
  )}&scope=${scopes}&response_type=code`;
};

/**
 * Exchange authorization code for access token
 * @param code Authorization code from Instagram login
 * @param clientId Facebook App ID
 * @param clientSecret Facebook App Secret
 * @param redirectUri Redirect URI used for authentication
 * @returns Access token and user data
 */
export const exchangeCodeForToken = async (
  code: string,
  clientId: string,
  clientSecret: string,
  redirectUri: string
) => {
  try {
    const response = await axios.get(
      `https://graph.facebook.com/${INSTAGRAM_API_VERSION}/oauth/access_token`,
      {
        params: {
          client_id: clientId,
          client_secret: clientSecret,
          redirect_uri: redirectUri,
          code: code,
        },
      }
    );

    const { access_token } = response.data;

    // Get long-lived token
    const longLivedTokenResponse = await axios.get(
      `https://graph.facebook.com/${INSTAGRAM_API_VERSION}/oauth/access_token`,
      {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: clientId,
          client_secret: clientSecret,
          fb_exchange_token: access_token,
        },
      }
    );

    const { access_token: longLivedToken, expires_in: longLivedExpiry } = longLivedTokenResponse.data;

    // Get user data
    const userDataResponse = await axios.get(`${INSTAGRAM_API_BASE_URL}/me`, {
      params: {
        fields: 'id,name,accounts',
        access_token: longLivedToken,
      },
    });

    return {
      accessToken: longLivedToken,
      expiresIn: longLivedExpiry,
      userId: userDataResponse.data.id,
      name: userDataResponse.data.name,
      accounts: userDataResponse.data.accounts,
    };
  } catch (error) {
    console.error('Error exchanging code for token:', error);
    throw error;
  }
};

/**
 * Get Instagram business accounts for a user
 * @param accessToken User access token
 * @returns List of Instagram business accounts
 */
export const getInstagramBusinessAccounts = async (accessToken: string) => {
  try {
    // First get Facebook pages
    const pagesResponse = await axios.get(`${INSTAGRAM_API_BASE_URL}/me/accounts`, {
      params: {
        access_token: accessToken,
      },
    });

    const pages = pagesResponse.data.data;
    const instagramAccounts = [];

    // For each page, get the Instagram business account
    for (const page of pages) {
      try {
        const instagramResponse = await axios.get(
          `${INSTAGRAM_API_BASE_URL}/${page.id}`,
          {
            params: {
              fields: 'instagram_business_account',
              access_token: accessToken,
            },
          }
        );

        if (instagramResponse.data.instagram_business_account) {
          // Get Instagram account details
          const instagramId = instagramResponse.data.instagram_business_account.id;
          const accountResponse = await axios.get(
            `${INSTAGRAM_API_BASE_URL}/${instagramId}`,
            {
              params: {
                fields: 'id,username,profile_picture_url,name,biography,follows_count,followers_count,media_count',
                access_token: accessToken,
              },
            }
          );

          instagramAccounts.push({
            id: instagramId,
            pageId: page.id,
            pageName: page.name,
            pageAccessToken: page.access_token,
            ...accountResponse.data,
          });
        }
      } catch (error) {
        console.error(`Error getting Instagram account for page ${page.id}:`, error);
      }
    }

    return instagramAccounts;
  } catch (error) {
    console.error('Error getting Instagram business accounts:', error);
    throw error;
  }
};

/**
 * Post an image to Instagram
 * @param instagramAccountId Instagram business account ID
 * @param accessToken Access token
 * @param imageUrl URL of the image to post
 * @param caption Post caption
 * @param options Additional posting options
 * @returns Post ID and status
 */
export const postImageToInstagram = async (
  instagramAccountId: string,
  accessToken: string,
  imageUrl: string,
  caption: string,
  options: {
    hashtags?: string[];
    firstComment?: string;
    locationName?: string;
    userTags?: Array<{ username: string; x: number; y: number }>;
  } = {}
) => {
  try {
    // Format caption with hashtags
    let fullCaption = caption || '';
    if (options.hashtags && options.hashtags.length > 0) {
      const formattedHashtags = formatInstagramHashtags(options.hashtags);
      fullCaption = `${fullCaption}\n\n${formattedHashtags}`;
    }

    // Create container
    const containerResponse = await axios.post(
      `${INSTAGRAM_API_BASE_URL}/${instagramAccountId}/media`,
      null,
      {
        params: {
          image_url: imageUrl,
          caption: fullCaption,
          access_token: accessToken,
        },
      }
    );

    const containerId = containerResponse.data.id;

    // Publish container
    const publishResponse = await axios.post(
      `${INSTAGRAM_API_BASE_URL}/${instagramAccountId}/media_publish`,
      null,
      {
        params: {
          creation_id: containerId,
          access_token: accessToken,
        },
      }
    );

    // Post first comment if provided
    if (options.firstComment) {
      await axios.post(
        `${INSTAGRAM_API_BASE_URL}/${publishResponse.data.id}/comments`,
        null,
        {
          params: {
            message: options.firstComment,
            access_token: accessToken,
          },
        }
      );
    }

    return {
      id: publishResponse.data.id,
      status: 'published',
    };
  } catch (error) {
    console.error('Error posting image to Instagram:', error);
    throw error;
  }
};

/**
 * Post a carousel to Instagram
 * @param instagramAccountId Instagram business account ID
 * @param accessToken Access token
 * @param imageUrls Array of image URLs for the carousel
 * @param caption Post caption
 * @param options Additional posting options
 * @returns Post ID and status
 */
export const postCarouselToInstagram = async (
  instagramAccountId: string,
  accessToken: string,
  imageUrls: string[],
  caption: string,
  options: {
    hashtags?: string[];
    firstComment?: string;
    locationName?: string;
    userTags?: Array<{ username: string; x: number; y: number }>;
  } = {}
) => {
  try {
    // Format caption with hashtags
    let fullCaption = caption || '';
    if (options.hashtags && options.hashtags.length > 0) {
      const formattedHashtags = formatInstagramHashtags(options.hashtags);
      fullCaption = `${fullCaption}\n\n${formattedHashtags}`;
    }

    // Create media objects for each image
    const mediaIds = [];
    for (const imageUrl of imageUrls) {
      const mediaResponse = await axios.post(
        `${INSTAGRAM_API_BASE_URL}/${instagramAccountId}/media`,
        null,
        {
          params: {
            image_url: imageUrl,
            is_carousel_item: true,
            access_token: accessToken,
          },
        }
      );
      mediaIds.push(mediaResponse.data.id);
    }

    // Create carousel container
    const containerResponse = await axios.post(
      `${INSTAGRAM_API_BASE_URL}/${instagramAccountId}/media`,
      null,
      {
        params: {
          media_type: 'CAROUSEL',
          children: mediaIds.join(','),
          caption: fullCaption,
          access_token: accessToken,
        },
      }
    );

    const containerId = containerResponse.data.id;

    // Publish carousel
    const publishResponse = await axios.post(
      `${INSTAGRAM_API_BASE_URL}/${instagramAccountId}/media_publish`,
      null,
      {
        params: {
          creation_id: containerId,
          access_token: accessToken,
        },
      }
    );

    // Post first comment if provided
    if (options.firstComment) {
      await axios.post(
        `${INSTAGRAM_API_BASE_URL}/${publishResponse.data.id}/comments`,
        null,
        {
          params: {
            message: options.firstComment,
            access_token: accessToken,
          },
        }
      );
    }

    return {
      id: publishResponse.data.id,
      status: 'published',
    };
  } catch (error) {
    console.error('Error posting carousel to Instagram:', error);
    throw error;
  }
};
