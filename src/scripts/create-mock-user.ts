import payload from 'payload';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '../../.env'),
});

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);

// Initialize Payload
const start = async () => {
  await payload.init({
    configPath: path.resolve(dirname, '../payload.config.ts'),
  });

  try {
    // Check if user already exists
    const existingUser = await payload.find({
      collection: 'users',
      where: {
        email: {
          equals: '<EMAIL>',
        },
      },
    });

    if (existingUser.docs.length > 0) {
      console.log('User already exists. Skipping creation.');
      process.exit(0);
    }

    // Create a mock admin user
    const mockUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        password: 'Password123!',
        name: 'Admin User',
        role: 'admin',
        phone: '+91 9876543210',
        subscription: {
          plan: 'enterprise',
          status: 'active',
          startDate: new Date().toISOString(),
          endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
        },
      },
    });

    console.log('Mock user created successfully:');
    console.log(`Email: ${mockUser.email}`);
    console.log(`Password: Password123!`);
    console.log(`Role: ${mockUser.role}`);

    // Create a mock regular user
    const mockRegularUser = await payload.create({
      collection: 'users',
      data: {
        email: '<EMAIL>',
        password: 'Password123!',
        name: 'Regular User',
        role: 'user',
        phone: '+91 9876543211',
        subscription: {
          plan: 'basic',
          status: 'active',
          startDate: new Date().toISOString(),
          endDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
        },
      },
    });

    console.log('\nMock regular user created successfully:');
    console.log(`Email: ${mockRegularUser.email}`);
    console.log(`Password: Password123!`);
    console.log(`Role: ${mockRegularUser.role}`);

    process.exit(0);
  } catch (error) {
    console.error('Error creating mock user:', error);
    process.exit(1);
  }
};

start();
