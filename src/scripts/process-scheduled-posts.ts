import payload from 'payload';
import path from 'path';
import dotenv from 'dotenv';
import { processScheduledPosts } from '../services/scheduler';

// Load environment variables
dotenv.config({
  path: path.resolve(__dirname, '../../.env'),
});

// Initialize Payload and process scheduled posts
const start = async () => {
  await payload.init({
    secret: process.env.PAYLOAD_SECRET || '',
    local: true,
    mongoURL: process.env.DATABASE_URI || '',
  });

  try {
    console.log('Processing scheduled social media posts...');
    await processScheduledPosts();
    console.log('Finished processing scheduled posts.');
    process.exit(0);
  } catch (error) {
    console.error('Error processing scheduled posts:', error);
    process.exit(1);
  }
};

start();
