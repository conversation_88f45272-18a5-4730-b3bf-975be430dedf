import express from 'express';
import payload from 'payload';
import { startScheduler } from './services/scheduler';

// Initialize Express app
const app = express();

// Initialize Payload
const start = async () => {
  // Initialize Payload
  await payload.init({
    secret: process.env.PAYLOAD_SECRET || '',
    express: app,
    onInit: () => {
      payload.logger.info(`Payload Admin URL: ${payload.getAdminURL()}`);
    },
  });

  // Start the social media post scheduler
  if (process.env.ENABLE_SCHEDULER === 'true') {
    const intervalMinutes = parseInt(process.env.SCHEDULER_INTERVAL_MINUTES || '5', 10);
    startScheduler(intervalMinutes);
    payload.logger.info(`Social media post scheduler started with ${intervalMinutes} minute interval`);
  }

  // Add your own express routes here if needed

  // Start the server
  const PORT = process.env.PORT || 3000;
  app.listen(PORT, () => {
    payload.logger.info(`Server started on port ${PORT}`);
  });
};

start();
