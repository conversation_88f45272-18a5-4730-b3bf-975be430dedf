/**
 * Instagram content validation utilities
 */

// Instagram caption character limit (2200 characters)
const INSTAGRAM_CAPTION_MAX_LENGTH = 2200;

// Instagram hashtag limit (30 hashtags)
const INSTAGRAM_HASHTAG_LIMIT = 30;

// Instagram supported image formats
export const INSTAGRAM_SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/png',
];

// Instagram supported video formats
export const INSTAGRAM_SUPPORTED_VIDEO_FORMATS = [
  'video/mp4',
  'video/quicktime',
];

// Instagram image aspect ratio limits
export const INSTAGRAM_IMAGE_ASPECT_RATIO = {
  min: 4 / 5, // 0.8
  max: 1.91 / 1, // 1.91
};

// Instagram image resolution requirements
export const INSTAGRAM_IMAGE_RESOLUTION = {
  minWidth: 320,
  maxWidth: 1440,
  minHeight: 320,
  maxHeight: 1440,
};

// Instagram video duration limits (in seconds)
export const INSTAGRAM_VIDEO_DURATION = {
  min: 3,
  max: 60,
};

/**
 * Validates Instagram caption length and content
 * @param caption The caption text to validate
 * @returns Object with validation result and error message if any
 */
export const validateInstagramCaption = (caption: string): { valid: boolean; message?: string } => {
  if (!caption) {
    return { valid: true };
  }

  if (caption.length > INSTAGRAM_CAPTION_MAX_LENGTH) {
    return {
      valid: false,
      message: `Caption exceeds Instagram's limit of ${INSTAGRAM_CAPTION_MAX_LENGTH} characters. Current length: ${caption.length}`,
    };
  }

  return { valid: true };
};

/**
 * Validates Instagram hashtags count
 * @param hashtags Array of hashtag strings
 * @returns Object with validation result and error message if any
 */
export const validateInstagramHashtags = (hashtags: string[]): { valid: boolean; message?: string } => {
  if (!hashtags || hashtags.length === 0) {
    return { valid: true };
  }

  if (hashtags.length > INSTAGRAM_HASHTAG_LIMIT) {
    return {
      valid: false,
      message: `Too many hashtags. Instagram allows a maximum of ${INSTAGRAM_HASHTAG_LIMIT} hashtags per post. Current count: ${hashtags.length}`,
    };
  }

  return { valid: true };
};

/**
 * Validates if a file is compatible with Instagram image requirements
 * @param mimeType The MIME type of the file
 * @param width Image width in pixels
 * @param height Image height in pixels
 * @returns Object with validation result and error message if any
 */
export const validateInstagramImage = (
  mimeType: string,
  width: number,
  height: number
): { valid: boolean; message?: string } => {
  // Check file format
  if (!INSTAGRAM_SUPPORTED_IMAGE_FORMATS.includes(mimeType)) {
    return {
      valid: false,
      message: `Unsupported file format. Instagram supports: ${INSTAGRAM_SUPPORTED_IMAGE_FORMATS.join(', ')}`,
    };
  }

  // Check image dimensions
  if (
    width < INSTAGRAM_IMAGE_RESOLUTION.minWidth ||
    width > INSTAGRAM_IMAGE_RESOLUTION.maxWidth ||
    height < INSTAGRAM_IMAGE_RESOLUTION.minHeight ||
    height > INSTAGRAM_IMAGE_RESOLUTION.maxHeight
  ) {
    return {
      valid: false,
      message: `Image dimensions must be between ${INSTAGRAM_IMAGE_RESOLUTION.minWidth}x${INSTAGRAM_IMAGE_RESOLUTION.minHeight} and ${INSTAGRAM_IMAGE_RESOLUTION.maxWidth}x${INSTAGRAM_IMAGE_RESOLUTION.maxHeight} pixels`,
    };
  }

  // Check aspect ratio
  const aspectRatio = width / height;
  if (aspectRatio < INSTAGRAM_IMAGE_ASPECT_RATIO.min || aspectRatio > INSTAGRAM_IMAGE_ASPECT_RATIO.max) {
    return {
      valid: false,
      message: `Image aspect ratio must be between ${INSTAGRAM_IMAGE_ASPECT_RATIO.min} and ${INSTAGRAM_IMAGE_ASPECT_RATIO.max}. Current ratio: ${aspectRatio.toFixed(2)}`,
    };
  }

  return { valid: true };
};

/**
 * Formats hashtags for Instagram
 * @param hashtags Array of hashtag strings
 * @returns Formatted hashtag string
 */
export const formatInstagramHashtags = (hashtags: string[]): string => {
  if (!hashtags || hashtags.length === 0) {
    return '';
  }

  return hashtags
    .map(tag => {
      // Ensure hashtag starts with #
      const formattedTag = tag.startsWith('#') ? tag : `#${tag}`;
      // Remove spaces and special characters
      return formattedTag.replace(/[^\w#]/g, '');
    })
    .join(' ');
};
