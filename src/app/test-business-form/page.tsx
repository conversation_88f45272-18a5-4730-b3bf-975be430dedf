'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestBusinessForm() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const formData = new FormData(e.currentTarget)

      // Log the form data for debugging
      console.log('Form data:')
      for (const [key, value] of formData.entries()) {
        console.log(`${key}: ${value instanceof File ? value.name : value}`)
      }

      // Send the form data to the real API
      const response = await fetch('/api/businesses/create-with-logo', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create business')
      }

      setResult(data)
    } catch (err: any) {
      console.error('Error creating business:', err)
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create Business</CardTitle>
          <CardDescription>Fill out the form to create a new business</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="businessName">Business Name *</Label>
              <Input id="businessName" name="businessName" required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="industry">Industry *</Label>
              <Select name="industry" required>
                <SelectTrigger>
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="food_beverage">Food & Beverage</SelectItem>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="professional_services">Professional Services</SelectItem>
                  <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="construction">Construction</SelectItem>
                  <SelectItem value="transportation">Transportation</SelectItem>
                  <SelectItem value="hospitality">Hospitality</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brandBrief">Brand Brief</Label>
              <Textarea id="brandBrief" name="brandBrief" placeholder="Describe your brand, values, and target audience" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="logo">Business Logo</Label>
              <Input id="logo" name="logo" type="file" accept="image/*" />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location.address">Address</Label>
                <Input id="location.address" name="location.address" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location.city">City</Label>
                <Input id="location.city" name="location.city" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location.state">State</Label>
                <Input id="location.state" name="location.state" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location.pincode">PIN Code</Label>
                <Input id="location.pincode" name="location.pincode" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contactInfo.email">Business Email</Label>
                <Input id="contactInfo.email" name="contactInfo.email" type="email" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactInfo.phone">Business Phone</Label>
                <Input id="contactInfo.phone" name="contactInfo.phone" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactInfo.website">Current Website (if any)</Label>
                <Input id="contactInfo.website" name="contactInfo.website" />
              </div>
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Business'}
            </Button>
          </form>

          {error && (
            <div className="mt-4 p-4 bg-red-50 text-red-600 rounded-md">
              <p className="font-medium">Error</p>
              <p>{error}</p>
            </div>
          )}

          {result && (
            <div className="mt-4 p-4 bg-green-50 text-green-600 rounded-md">
              <p className="font-medium">Success!</p>
              <p>Business created with ID: {result.id}</p>
              <pre className="mt-2 text-xs overflow-auto max-h-40">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
