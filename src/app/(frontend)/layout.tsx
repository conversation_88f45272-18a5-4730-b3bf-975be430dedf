import React from 'react'
import Link from 'next/link'
import { Building2, BarChart3, Calendar, Settings, Sparkles } from 'lucide-react'
import './styles.css'

export const metadata = {
  description: 'AI-powered business management platform',
  title: 'Dukanify - AI Business Solutions',
}

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: BarChart3 },
  { name: 'Business', href: '/business', icon: Building2 },
  { name: 'Social Media', href: '/social-media', icon: Calendar },
  { name: 'AI Content Generator', href: '/ai-content-generator', icon: Sparkles },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export default async function RootLayout(props: { children: React.ReactNode }) {
  const { children } = props

  return (
    <html lang="en">
      <body className="bg-gray-50">
        <div className="min-h-screen">
          {/* Navigation */}
          <nav className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between h-16">
                <div className="flex">
                  <div className="flex-shrink-0 flex items-center">
                    <Link href="/" className="text-2xl font-bold text-blue-600">
                      Dukanify
                    </Link>
                  </div>
                  <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                    {navigation.map((item) => {
                      const Icon = item.icon
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-500 hover:text-gray-900 hover:border-gray-300 border-b-2 border-transparent transition-colors"
                        >
                          <Icon className="h-4 w-4 mr-2" />
                          {item.name}
                        </Link>
                      )
                    })}
                  </div>
                </div>
                <div className="flex items-center">
                  <Link
                    href="/admin"
                    className="text-sm text-gray-500 hover:text-gray-900 transition-colors"
                  >
                    Admin Panel
                  </Link>
                </div>
              </div>
            </div>
          </nav>

          {/* Main Content */}
          <main className="bg-gray-50 min-h-screen">{children}</main>
        </div>
      </body>
    </html>
  )
}
