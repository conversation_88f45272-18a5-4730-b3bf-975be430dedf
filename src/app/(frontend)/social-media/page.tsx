"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import SocialMediaAccounts from "@/components/social-media/accounts"
import SocialMediaContent from "@/components/social-media/content"
import SocialMediaSchedule from "@/components/social-media/schedule"
import SocialMediaAnalytics from "@/components/social-media/analytics"

export default function SocialMediaPage() {
  const [activeTab, setActiveTab] = useState("content")

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Social Media Management</h1>
      
      <Tabs defaultValue="content" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="content">Content Creation</TabsTrigger>
          <TabsTrigger value="accounts">Connected Accounts</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="content" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Create Social Media Content</CardTitle>
              <CardDescription>
                Create and schedule posts for your connected social media accounts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SocialMediaContent />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="accounts" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Connected Social Media Accounts</CardTitle>
              <CardDescription>
                Manage your connected social media accounts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SocialMediaAccounts />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="schedule" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Content Schedule</CardTitle>
              <CardDescription>
                View and manage your scheduled social media posts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SocialMediaSchedule />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Social Media Analytics</CardTitle>
              <CardDescription>
                Track the performance of your social media posts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SocialMediaAnalytics />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
