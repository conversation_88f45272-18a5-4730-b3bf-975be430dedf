"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import BusinessSelector from "@/components/ai-content/business-selector"
import GenerationControls from "@/components/ai-content/generation-controls"
import ContentGrid from "@/components/ai-content/content-grid"
import { ArrowLeft, Sparkles, CheckCircle } from "lucide-react"

interface Business {
  id: string
  businessName: string
  industry: string
  brandBrief?: string
  brandElements?: {
    colorPalette?: Array<{
      colorHex: string
      colorName: string
    }>
    seoKeywords?: Array<{
      keyword: string
      relevanceScore: number
    }>
  }
}

interface GeneratedPost {
  id: string
  title: string
  caption: string
  hashtags: string[]
  platform: string
  contentType: string
  imageUrl: string
  visualDescription: string
  businessName: string
}

interface GenerationSettings {
  platforms: string[]
  contentTypes: string[]
  tone: string
  focusAreas: string[]
  customPrompt?: string
}

type Step = "select-business" | "configure-generation" | "view-content"

export default function AIContentGeneratorPage() {
  const [currentStep, setCurrentStep] = useState<Step>("select-business")
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null)
  const [generatedPosts, setGeneratedPosts] = useState<GeneratedPost[]>([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [savedCount, setSavedCount] = useState(0)

  const handleBusinessSelect = (business: Business) => {
    setSelectedBusiness(business)
    setCurrentStep("configure-generation")
  }

  const handleGenerate = async (settings: GenerationSettings) => {
    setIsGenerating(true)
    setCurrentStep("view-content")
    
    // Simulate AI generation process
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // For demo purposes, we'll use the dummy data from ContentGrid
      // In real implementation, this would call the AI service
      setGeneratedPosts([]) // ContentGrid will show dummy data
      
    } catch (error) {
      console.error("Generation failed:", error)
      // Handle error
    } finally {
      setIsGenerating(false)
    }
  }

  const handleRegenerateAll = async () => {
    setIsGenerating(true)
    // Simulate regeneration
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsGenerating(false)
  }

  const handleRegeneratePost = async (postId: string) => {
    // Simulate single post regeneration
    console.log("Regenerating post:", postId)
  }

  const handleEditPost = (postId: string, updatedPost: Partial<GeneratedPost>) => {
    setGeneratedPosts(prev => 
      prev.map(post => 
        post.id === postId ? { ...post, ...updatedPost } : post
      )
    )
  }

  const handleSaveAll = async (selectedPosts: GeneratedPost[]) => {
    setIsSaving(true)
    try {
      // Simulate saving to CMS
      await new Promise(resolve => setTimeout(resolve, 2000))
      setSavedCount(selectedPosts.length)
      
      // Show success message
      setTimeout(() => setSavedCount(0), 3000)
    } catch (error) {
      console.error("Save failed:", error)
    } finally {
      setIsSaving(false)
    }
  }

  const handleBackToBusinessSelection = () => {
    setCurrentStep("select-business")
    setSelectedBusiness(null)
    setGeneratedPosts([])
  }

  const handleBackToConfiguration = () => {
    setCurrentStep("configure-generation")
    setGeneratedPosts([])
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <Sparkles className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold">AI Content Generator</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Generate engaging social media content powered by AI, tailored to your business brand and audience
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-center space-x-4">
        <div className={`flex items-center space-x-2 ${
          currentStep === "select-business" ? "text-blue-600" : 
          currentStep !== "select-business" ? "text-green-600" : "text-gray-400"
        }`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            currentStep === "select-business" ? "bg-blue-600 text-white" :
            currentStep !== "select-business" ? "bg-green-600 text-white" : "bg-gray-200"
          }`}>
            {currentStep !== "select-business" ? <CheckCircle className="h-4 w-4" /> : "1"}
          </div>
          <span className="font-medium">Select Business</span>
        </div>
        
        <div className="w-12 h-px bg-gray-300"></div>
        
        <div className={`flex items-center space-x-2 ${
          currentStep === "configure-generation" ? "text-blue-600" : 
          currentStep === "view-content" ? "text-green-600" : "text-gray-400"
        }`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            currentStep === "configure-generation" ? "bg-blue-600 text-white" :
            currentStep === "view-content" ? "bg-green-600 text-white" : "bg-gray-200"
          }`}>
            {currentStep === "view-content" ? <CheckCircle className="h-4 w-4" /> : "2"}
          </div>
          <span className="font-medium">Configure Generation</span>
        </div>
        
        <div className="w-12 h-px bg-gray-300"></div>
        
        <div className={`flex items-center space-x-2 ${
          currentStep === "view-content" ? "text-blue-600" : "text-gray-400"
        }`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            currentStep === "view-content" ? "bg-blue-600 text-white" : "bg-gray-200"
          }`}>
            3
          </div>
          <span className="font-medium">Review & Publish</span>
        </div>
      </div>

      {/* Success Message */}
      {savedCount > 0 && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">
                Successfully saved {savedCount} post{savedCount !== 1 ? 's' : ''} to your social media content!
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Step Content */}
      {currentStep === "select-business" && (
        <BusinessSelector 
          onBusinessSelect={handleBusinessSelect}
          selectedBusinessId={selectedBusiness?.id}
        />
      )}

      {currentStep === "configure-generation" && selectedBusiness && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <Button 
                variant="outline" 
                onClick={handleBackToBusinessSelection}
                className="mb-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Business Selection
              </Button>
              <GenerationControls
                business={selectedBusiness}
                onGenerate={handleGenerate}
                isGenerating={isGenerating}
              />
            </div>
          </div>
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Ready to Generate Content</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  Configure your generation settings on the left, then click "Generate 9 Posts" to create 
                  AI-powered social media content tailored to your business.
                </p>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">What you'll get:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• 9 unique, brand-consistent social media posts</li>
                    <li>• Platform-optimized content and formatting</li>
                    <li>• Relevant hashtags and engaging captions</li>
                    <li>• Visual descriptions for image generation</li>
                    <li>• Editable content before publishing</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {currentStep === "view-content" && selectedBusiness && (
        <div>
          <div className="flex items-center gap-4 mb-6">
            <Button 
              variant="outline" 
              onClick={handleBackToConfiguration}
              disabled={isGenerating || isSaving}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Configuration
            </Button>
          </div>
          
          <ContentGrid
            posts={generatedPosts}
            isGenerating={isGenerating}
            onRegenerate={handleRegenerateAll}
            onRegeneratePost={handleRegeneratePost}
            onEditPost={handleEditPost}
            onSaveAll={handleSaveAll}
            businessName={selectedBusiness.businessName}
          />
        </div>
      )}
    </div>
  )
}
