'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import Image from 'next/image'

interface Business {
  id: string
  businessName: string
  logo?: {
    id: string
    url: string
  } | null
  brandBrief?: string
  industry: string
  location?: {
    address?: string
    city?: string
    state?: string
    pincode?: string
  }
  contactInfo?: {
    email?: string
    phone?: string
    website?: string
  }
}

export default function BusinessDetailsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const businessId = searchParams.get('id')
  
  const [business, setBusiness] = useState<Business | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('basic')

  useEffect(() => {
    if (!businessId) {
      setError('No business ID provided')
      setLoading(false)
      return
    }

    const fetchBusiness = async () => {
      try {
        const response = await fetch(`/api/businesses?id=${businessId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch business details')
        }
        
        const data = await response.json()
        setBusiness(data)
      } catch (err: any) {
        console.error('Error fetching business:', err)
        setError(err.message || 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchBusiness()
  }, [businessId])

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setLogoFile(file)
    
    // Create a preview URL
    const previewUrl = URL.createObjectURL(file)
    setLogoPreview(previewUrl)
    
    // Clean up the preview URL when component unmounts
    return () => URL.revokeObjectURL(previewUrl)
  }

  const handleLogoUpload = async () => {
    if (!logoFile || !businessId) return
    
    setError(null)
    setSaving(true)
    
    try {
      const formData = new FormData()
      formData.append('file', logoFile)
      
      // Log the form data for debugging
      console.log('Uploading logo for business:', businessId)
      console.log('Logo file:', logoFile.name, logoFile.type, logoFile.size)
      
      const response = await fetch(`/api/businesses/${businessId}/logo`, {
        method: 'POST',
        body: formData,
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to upload logo')
      }
      
      // Update the business state with the new logo
      if (business) {
        setBusiness({
          ...business,
          logo: data.doc.logo,
        })
      }
      
      setSuccess('Logo uploaded successfully')
      
      // Clear the file input
      setLogoFile(null)
    } catch (err: any) {
      console.error('Error uploading logo:', err)
      setError(err.message || 'An error occurred')
    } finally {
      setSaving(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!businessId) return
    
    setError(null)
    setSuccess(null)
    setSaving(true)
    
    try {
      const formData = new FormData(e.currentTarget)
      
      // Convert form data to object
      const data: Record<string, any> = {}
      for (const [key, value] of formData.entries()) {
        if (key === 'logo') continue // Skip logo as we handle it separately
        
        // Handle nested objects with dot notation (e.g., location.city)
        if (key.includes('.')) {
          const [parent, child] = key.split('.')
          if (!data[parent]) data[parent] = {}
          data[parent][child] = value
        } else {
          data[key] = value
        }
      }
      
      // Add the ID to the data
      data.id = businessId
      
      // Log the data for debugging
      console.log('Updating business with data:', data)
      
      const response = await fetch('/api/businesses', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      
      const responseData = await response.json()
      
      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to update business')
      }
      
      setBusiness(responseData.doc)
      setSuccess('Business details updated successfully')
    } catch (err: any) {
      console.error('Error updating business:', err)
      setError(err.message || 'An error occurred')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-10">
        <Card>
          <CardContent className="p-10 text-center">
            <p>Loading business details...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error && !business) {
    return (
      <div className="container mx-auto py-10">
        <Card>
          <CardContent className="p-10">
            <div className="flex items-center gap-2 text-red-600 mb-4">
              <AlertCircle size={20} />
              <p>{error}</p>
            </div>
            <Button onClick={() => router.push('/dashboard')}>Back to Dashboard</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Business Details</h1>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="basic">Basic Information</TabsTrigger>
          <TabsTrigger value="branding">Branding</TabsTrigger>
          <TabsTrigger value="contact">Contact & Location</TabsTrigger>
        </TabsList>
        
        <form onSubmit={handleSubmit} className="space-y-6 mt-6">
          <TabsContent value="basic">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Update your business's basic details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input 
                    id="businessName" 
                    name="businessName" 
                    defaultValue={business?.businessName} 
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="industry">Industry *</Label>
                  <Select name="industry" defaultValue={business?.industry} required>
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="food_beverage">Food & Beverage</SelectItem>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="healthcare">Healthcare</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="professional_services">Professional Services</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="construction">Construction</SelectItem>
                      <SelectItem value="transportation">Transportation</SelectItem>
                      <SelectItem value="hospitality">Hospitality</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="branding">
            <Card>
              <CardHeader>
                <CardTitle>Branding</CardTitle>
                <CardDescription>Update your business's branding elements</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <Label>Business Logo</Label>
                  <div className="flex flex-col md:flex-row gap-4 items-start">
                    <div className="w-32 h-32 border rounded-md flex items-center justify-center overflow-hidden bg-gray-50">
                      {(logoPreview || business?.logo?.url) ? (
                        <Image 
                          src={logoPreview || business?.logo?.url || ''} 
                          alt="Business Logo" 
                          width={128} 
                          height={128} 
                          className="object-contain" 
                        />
                      ) : (
                        <p className="text-gray-400 text-sm text-center p-2">No logo uploaded</p>
                      )}
                    </div>
                    <div className="flex-1 space-y-2">
                      <Input 
                        id="logo" 
                        name="logo" 
                        type="file" 
                        accept="image/*" 
                        onChange={handleLogoChange} 
                      />
                      <Button 
                        type="button" 
                        onClick={handleLogoUpload} 
                        disabled={!logoFile || saving}
                        variant="outline"
                      >
                        {saving ? 'Uploading...' : 'Upload Logo'}
                      </Button>
                      <p className="text-sm text-gray-500">
                        Recommended size: 512x512 pixels. Max file size: 2MB.
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="brandBrief">Brand Brief</Label>
                  <Textarea 
                    id="brandBrief" 
                    name="brandBrief" 
                    defaultValue={business?.brandBrief || ''} 
                    placeholder="Describe your brand, values, and target audience" 
                    className="min-h-32"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="contact">
            <Card>
              <CardHeader>
                <CardTitle>Contact & Location</CardTitle>
                <CardDescription>Update your business's contact information and location</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Contact Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="contactInfo.email">Business Email</Label>
                        <Input 
                          id="contactInfo.email" 
                          name="contactInfo.email" 
                          type="email" 
                          defaultValue={business?.contactInfo?.email || ''} 
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contactInfo.phone">Business Phone</Label>
                        <Input 
                          id="contactInfo.phone" 
                          name="contactInfo.phone" 
                          defaultValue={business?.contactInfo?.phone || ''} 
                        />
                      </div>
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="contactInfo.website">Business Website</Label>
                        <Input 
                          id="contactInfo.website" 
                          name="contactInfo.website" 
                          defaultValue={business?.contactInfo?.website || ''} 
                          placeholder="https://example.com" 
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Location</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2 md:col-span-2">
                        <Label htmlFor="location.address">Address</Label>
                        <Input 
                          id="location.address" 
                          name="location.address" 
                          defaultValue={business?.location?.address || ''} 
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location.city">City</Label>
                        <Input 
                          id="location.city" 
                          name="location.city" 
                          defaultValue={business?.location?.city || ''} 
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location.state">State</Label>
                        <Input 
                          id="location.state" 
                          name="location.state" 
                          defaultValue={business?.location?.state || ''} 
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location.pincode">PIN Code</Label>
                        <Input 
                          id="location.pincode" 
                          name="location.pincode" 
                          defaultValue={business?.location?.pincode || ''} 
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <div className="flex flex-col gap-4">
            {error && (
              <div className="flex items-center gap-2 text-red-600 p-4 bg-red-50 rounded-md">
                <AlertCircle size={20} />
                <p>{error}</p>
              </div>
            )}
            
            {success && (
              <div className="flex items-center gap-2 text-green-600 p-4 bg-green-50 rounded-md">
                <CheckCircle2 size={20} />
                <p>{success}</p>
              </div>
            )}
            
            <div className="flex justify-between">
              <Button type="button" variant="outline" onClick={() => router.push('/dashboard')}>
                Back to Dashboard
              </Button>
              <Button type="submit" disabled={saving}>
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </form>
      </Tabs>
    </div>
  )
}
