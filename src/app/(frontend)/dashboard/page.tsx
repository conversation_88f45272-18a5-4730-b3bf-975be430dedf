'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Globe, Instagram, FileText, BarChart, Building2, Sparkles } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState, useEffect } from 'react'

export default function DashboardPage() {
  const router = useRouter()
  const [businesses, setBusinesses] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    // Fetch businesses
    const fetchBusinesses = async () => {
      try {
        const response = await fetch('/api/businesses')
        const data = await response.json()

        if (data.docs) {
          setBusinesses(data.docs)
        }
      } catch (err) {
        console.error('Error fetching businesses:', err)
        setError('Failed to load businesses')
      } finally {
        setLoading(false)
      }
    }

    fetchBusinesses()
  }, [])

  const handleBusinessClick = (businessId) => {
    router.push(`/business/details?id=${businessId}`)
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-br from-purple-50 to-blue-50 border-purple-200">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">AI Content Generator</CardTitle>
                <CardDescription>Generate social media posts with AI</CardDescription>
              </div>
              <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                New
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <Sparkles className="h-8 w-8 text-purple-600" />
              <Button
                onClick={() => router.push('/ai-content-generator')}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Generate
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Business</CardTitle>
            <CardDescription>Manage your business</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <Building2 className="h-8 w-8 text-amber-500" />
              {businesses.length > 0 ? (
                <Button onClick={() => handleBusinessClick(businesses[0].id)}>View</Button>
              ) : (
                <Button onClick={() => router.push('/test-business-form')}>Create</Button>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Websites</CardTitle>
            <CardDescription>Manage your websites</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <Globe className="h-8 w-8 text-blue-500" />
              <Button onClick={() => router.push('/websites')}>View</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Social Media</CardTitle>
            <CardDescription>Manage your social media</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <Instagram className="h-8 w-8 text-pink-500" />
              <Button onClick={() => router.push('/social-media')}>View</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Invoices</CardTitle>
            <CardDescription>Manage your invoices</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center">
              <FileText className="h-8 w-8 text-green-500" />
              <Button onClick={() => router.push('/invoices')}>View</Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-10">
        <h2 className="text-2xl font-bold mb-4">Recent Activity</h2>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-start gap-4 pb-4 border-b">
                <div className="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center">
                  <Instagram className="h-5 w-5 text-pink-500" />
                </div>
                <div>
                  <h3 className="font-medium">Instagram Post Scheduled</h3>
                  <p className="text-sm text-gray-500">
                    Your post &quot;Summer Collection&quot; has been scheduled for tomorrow at 10:00
                    AM
                  </p>
                  <p className="text-xs text-gray-400 mt-1">2 hours ago</p>
                </div>
              </div>

              <div className="flex items-start gap-4 pb-4 border-b">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                  <Globe className="h-5 w-5 text-blue-500" />
                </div>
                <div>
                  <h3 className="font-medium">Website Updated</h3>
                  <p className="text-sm text-gray-500">
                    Your website &quot;My Online Store&quot; has been updated with new products
                  </p>
                  <p className="text-xs text-gray-400 mt-1">Yesterday</p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                  <FileText className="h-5 w-5 text-green-500" />
                </div>
                <div>
                  <h3 className="font-medium">Invoice Paid</h3>
                  <p className="text-sm text-gray-500">
                    Invoice #1234 has been paid by Customer XYZ
                  </p>
                  <p className="text-xs text-gray-400 mt-1">2 days ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
