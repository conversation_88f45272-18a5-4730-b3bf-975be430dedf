import { NextRequest, NextResponse } from 'next/server'
import payload from 'payload'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Get a specific social media content
    const content = await payload.findByID({
      collection: 'social-media-content',
      id,
      depth: 2,
    })

    return NextResponse.json(content)
  } catch (error: any) {
    console.error('Error fetching social media content:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    // Update a social media content
    const content = await payload.update({
      collection: 'social-media-content',
      id,
      data: body,
    })

    return NextResponse.json(content)
  } catch (error: any) {
    console.error('Error updating social media content:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Delete a social media content
    await payload.delete({
      collection: 'social-media-content',
      id,
    })

    return NextResponse.json({ success: true })
  } catch (error: any) {
    console.error('Error deleting social media content:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}
