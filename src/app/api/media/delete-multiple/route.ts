import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function POST(request: NextRequest) {
  try {
    // Get the IDs from the request body
    const body = await request.json()
    const { ids } = body
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'No media IDs provided for deletion' },
        { status: 400 }
      )
    }
    
    // Initialize Payload
    const payload = await getPayload({
      config,
    })
    
    // Delete each media item
    const results = await Promise.all(
      ids.map(async (id) => {
        try {
          return await payload.delete({
            collection: 'media',
            id,
          })
        } catch (error) {
          console.error(`Error deleting media with ID ${id}:`, error)
          return { id, error: error.message || 'Failed to delete' }
        }
      })
    )
    
    return NextResponse.json({
      success: true,
      deletedCount: results.filter(r => !r.error).length,
      results,
    })
  } catch (error: any) {
    console.error('Error deleting media:', error)
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}
