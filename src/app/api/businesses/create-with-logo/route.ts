import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'
import os from 'os'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function POST(request: NextRequest) {
  let tempFilePath = '';

  try {
    // Log the request headers for debugging
    console.log('Request headers:', Object.fromEntries(request.headers.entries()));

    // Get the form data
    const formData = await request.formData()

    // Log the form data for debugging
    console.log('Form data keys:', [...formData.keys()]);

    // Extract the file
    const file = formData.get('logo') as File

    if (file) {
      console.log('Logo file:', file.name, file.type, file.size);
    }

    // Extract business data
    const businessData: Record<string, any> = {}

    // Process form data
    for (const [key, value] of formData.entries()) {
      if (key === 'logo') continue; // Skip logo as we handle it separately

      // Handle nested objects with dot notation (e.g., location.city)
      if (key.includes('.')) {
        const [parent, child] = key.split('.');
        if (!businessData[parent]) businessData[parent] = {};
        businessData[parent][child] = value;
      } else {
        businessData[key] = value;
      }
    }

    // Check for required fields
    if (!businessData.businessName) {
      return NextResponse.json(
        { error: 'Business Name is required' },
        { status: 400 }
      )
    }

    if (!businessData.industry) {
      return NextResponse.json(
        { error: 'Industry is required' },
        { status: 400 }
      )
    }

    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    // If a logo file was provided, upload it
    let mediaId = null;
    if (file && file instanceof File) {
      try {
        // Generate a unique filename to avoid conflicts
        const uniqueFilename = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

        // Create a temporary file
        const tempDir = os.tmpdir()
        tempFilePath = path.join(tempDir, uniqueFilename)

        // Write the file to disk
        const buffer = Buffer.from(await file.arrayBuffer())
        fs.writeFileSync(tempFilePath, buffer)

        // Upload the file to Payload
        const media = await payload.create({
          collection: 'media',
          data: {
            alt: file.name,
          },
          filePath: tempFilePath,
        })

        // Store the media ID
        mediaId = media.id;
        console.log('Media created successfully:', media.id);

        // Clean up the temporary file
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath)
        }
      } catch (mediaError) {
        console.error('Error uploading logo:', mediaError)
        // Continue without the logo if there's an error
      }
    }

    // Add the logo to the business data if we successfully uploaded it
    if (mediaId) {
      businessData.logo = mediaId;
    }

    // Get the current user
    let userId = null;
    try {
      const { user } = await payload.findGlobal({
        slug: 'current-user',
        depth: 0,
      })

      if (user && user.id) {
        userId = user.id;
        console.log('Found current user:', userId);
      }
    } catch (userError) {
      console.error('Error getting current user:', userError)
    }

    // If we couldn't get the current user, try to find any user
    if (!userId) {
      try {
        const users = await payload.find({
          collection: 'users',
          limit: 1,
        })

        if (users.docs && users.docs.length > 0) {
          userId = users.docs[0].id;
          console.log('Found default user:', userId);
        }
      } catch (findUserError) {
        console.error('Error finding a default user:', findUserError)
      }
    }

    // Add the owner field if we found a user
    if (userId) {
      businessData.owner = userId;
    } else {
      // If we still don't have a user, create a mock one for testing
      console.log('No user found, creating a mock business without owner');
    }

    console.log('Creating business with data:', JSON.stringify(businessData, null, 2))

    // Create the business
    try {
      const business = await payload.create({
        collection: 'businesses',
        data: businessData,
      })

      console.log('Business created successfully:', business.id);

      return NextResponse.json({
        id: business.id,
        doc: business,
        message: 'Business created successfully'
      })
    } catch (createError: any) {
      console.error('Error creating business:', createError);

      // If there's a validation error, return a more helpful message
      if (createError.name === 'ValidationError' && createError.data) {
        const invalidFields = Object.keys(createError.data).join(', ');
        return NextResponse.json(
          {
            error: `Validation error: ${invalidFields}`,
            details: createError.data
          },
          { status: 400 }
        )
      }

      throw createError;
    }
  } catch (error: any) {
    console.error('Error creating business with logo:', error)

    // Clean up the temporary file if it exists
    if (tempFilePath && fs.existsSync(tempFilePath)) {
      try {
        fs.unlinkSync(tempFilePath)
      } catch (cleanupError) {
        console.error('Error cleaning up temporary file:', cleanupError)
      }
    }

    // Create a mock business response for development
    if (process.env.NODE_ENV === 'development') {
      console.log('Returning mock business response for development');

      return NextResponse.json({
        id: `mock-${Date.now()}`,
        doc: {
          id: `mock-${Date.now()}`,
          businessName: 'Mock Business (Error Fallback)',
          industry: 'technology',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        message: 'Mock business created (due to error in real creation)'
      })
    }

    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}
