import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Check if the request is form data or JSON
    const contentType = request.headers.get('content-type') || '';
    let data;
    
    if (contentType.includes('multipart/form-data')) {
      // Handle form data
      const formData = await request.formData();
      
      // Convert form data to object
      data = {};
      for (const [key, value] of formData.entries()) {
        if (key === 'logo') {
          // Handle file
          const file = value as File;
          data.logo = {
            filename: file.name,
            mimeType: file.type,
            filesize: file.size,
          };
        } else if (key.includes('.')) {
          // Handle nested objects with dot notation (e.g., location.city)
          const [parent, child] = key.split('.');
          if (!data[parent]) data[parent] = {};
          data[parent][child] = value;
        } else {
          data[key] = value;
        }
      }
    } else {
      // Handle JSON data
      try {
        data = await request.json();
      } catch (jsonError) {
        console.error('Error parsing JSON:', jsonError);
        return NextResponse.json(
          { error: 'Invalid JSON in request body' },
          { status: 400 }
        );
      }
    }
    
    // Log the data for debugging
    console.log('Mock creating business with data:', JSON.stringify(data, null, 2));
    
    // Create a mock business response
    const mockBusiness = {
      id: `mock-${Date.now()}`,
      businessName: data.businessName || 'Mock Business',
      industry: data.industry || 'technology',
      logo: data.logo || null,
      location: data.location || {},
      contactInfo: data.contactInfo || {},
      owner: data.owner || `mock-user-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    return NextResponse.json({
      id: mockBusiness.id,
      doc: mockBusiness,
      message: 'Business created successfully (mock)'
    });
  } catch (error: any) {
    console.error('Error in mock business creation:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
