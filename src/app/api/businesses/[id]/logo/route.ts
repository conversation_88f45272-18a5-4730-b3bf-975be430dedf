import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'
import os from 'os'
import { getPayload } from 'payload'
import config from '@/payload.config'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  let tempFilePath = '';

  try {
    const { id } = await params

    // Get the form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Generate a unique filename to avoid conflicts
    const uniqueFilename = `${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`;

    // Create a temporary file
    const tempDir = os.tmpdir()
    tempFilePath = path.join(tempDir, uniqueFilename)

    // Write the file to disk
    const buffer = Buffer.from(await file.arrayBuffer())
    fs.writeFileSync(tempFilePath, buffer)

    // Initialize Payload
    const payload = await getPayload({
      config,
    })

    // Upload the file to Payload
    const media = await payload.create({
      collection: 'media',
      data: {
        alt: file.name,
      },
      filePath: tempFilePath,
    })

    // Update the business with the new logo
    const business = await payload.update({
      collection: 'businesses',
      id,
      data: {
        logo: media.id,
      },
    })

    // Clean up the temporary file
    if (fs.existsSync(tempFilePath)) {
      fs.unlinkSync(tempFilePath)
    }

    // Return a properly structured response
    return NextResponse.json({
      id: business.id,
      doc: business,
      message: 'Business logo updated successfully',
      media: {
        id: media.id,
        doc: media
      }
    })
  } catch (error: any) {
    console.error('Error uploading business logo:', error)

    // Clean up the temporary file if it exists
    if (tempFilePath && fs.existsSync(tempFilePath)) {
      try {
        fs.unlinkSync(tempFilePath)
      } catch (cleanupError) {
        console.error('Error cleaning up temporary file:', cleanupError)
      }
    }

    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    )
  }
}
