import { NextRequest, NextResponse } from 'next/server';
import payload from 'payload';
import { exchangeCodeForToken, getInstagramBusinessAccounts } from '../../../../services/instagram';

// Instagram API credentials (should be stored in environment variables)
const INSTAGRAM_CLIENT_ID = process.env.INSTAGRAM_CLIENT_ID || '';
const INSTAGRAM_CLIENT_SECRET = process.env.INSTAGRAM_CLIENT_SECRET || '';
const INSTAGRAM_REDIRECT_URI = process.env.INSTAGRAM_REDIRECT_URI || '';

/**
 * GET handler for Instagram OAuth callback
 * Processes the authorization code and creates/updates social media account
 */
export async function GET(request: NextRequest) {
  try {
    // Check if Instagram API credentials are configured
    if (!INSTAGRAM_CLIENT_ID || !INSTAGRAM_CLIENT_SECRET || !INSTAGRAM_REDIRECT_URI) {
      return NextResponse.json(
        { error: 'Instagram API credentials not configured' },
        { status: 500 }
      );
    }

    // Get authorization code and state from query parameters
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get('code');
    const stateParam = searchParams.get('state');

    if (!code) {
      return NextResponse.json(
        { error: 'Authorization code is missing' },
        { status: 400 }
      );
    }

    if (!stateParam) {
      return NextResponse.json(
        { error: 'State parameter is missing' },
        { status: 400 }
      );
    }

    // Decode state parameter to get businessId
    const state = JSON.parse(Buffer.from(stateParam, 'base64').toString());
    const { businessId } = state;

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is missing from state parameter' },
        { status: 400 }
      );
    }

    // Exchange authorization code for access token
    const tokenData = await exchangeCodeForToken(
      code,
      INSTAGRAM_CLIENT_ID,
      INSTAGRAM_CLIENT_SECRET,
      INSTAGRAM_REDIRECT_URI
    );

    // Get Instagram business accounts
    const instagramAccounts = await getInstagramBusinessAccounts(tokenData.accessToken);

    if (instagramAccounts.length === 0) {
      return NextResponse.json(
        { error: 'No Instagram business accounts found. Please convert your Instagram account to a business account.' },
        { status: 400 }
      );
    }

    // For each Instagram account, create or update a social media account
    for (const instagramAccount of instagramAccounts) {
      // Check if account already exists
      const existingAccounts = await payload.find({
        collection: 'social-media-accounts',
        where: {
          and: [
            {
              'authDetails.userId': {
                equals: instagramAccount.id,
              },
            },
            {
              platform: {
                equals: 'instagram',
              },
            },
          ],
        },
      });

      const accountData = {
        accountName: instagramAccount.username,
        business: businessId,
        platform: 'instagram' as const,
        status: 'connected' as const,
        username: instagramAccount.username,
        profileUrl: `https://www.instagram.com/${instagramAccount.username}`,
        authDetails: {
          accessToken: tokenData.accessToken,
          userId: instagramAccount.id,
          tokenExpiry: new Date(Date.now() + tokenData.expiresIn * 1000).toISOString(),
        },
        instagramSpecific: {
          businessAccount: true,
          mediaCount: instagramAccount.media_count,
          followerCount: instagramAccount.followers_count,
          followingCount: instagramAccount.follows_count,
        },
        lastSyncedAt: new Date().toISOString(),
        lastSyncStatus: 'success' as const,
      };

      if (existingAccounts.docs.length > 0) {
        // Update existing account
        await payload.update({
          collection: 'social-media-accounts',
          id: existingAccounts.docs[0].id,
          data: accountData,
        });
      } else {
        // Create new account
        await payload.create({
          collection: 'social-media-accounts',
          data: accountData,
        });
      }
    }

    // Redirect to success page
    return NextResponse.redirect(`${process.env.NEXT_PUBLIC_SERVER_URL}/social-media/accounts?success=true`);
  } catch (error: any) {
    console.error('Error processing Instagram callback:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_SERVER_URL}/social-media/accounts?error=${encodeURIComponent(
        error.message || 'An error occurred'
      )}`
    );
  }
}
