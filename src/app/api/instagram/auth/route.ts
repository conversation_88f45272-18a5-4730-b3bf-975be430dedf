import { NextRequest, NextResponse } from 'next/server';
import { getInstagramLoginUrl } from '../../../../services/instagram';

// Instagram API credentials (should be stored in environment variables)
const INSTAGRAM_CLIENT_ID = process.env.INSTAGRAM_CLIENT_ID || '';
const INSTAGRAM_CLIENT_SECRET = process.env.INSTAGRAM_CLIENT_SECRET || '';
const INSTAGRAM_REDIRECT_URI = process.env.INSTAGRAM_REDIRECT_URI || '';

/**
 * GET handler for Instagram authentication
 * Redirects the user to Instagram login page
 */
export async function GET(request: NextRequest) {
  try {
    // Check if Instagram API credentials are configured
    if (!INSTAGRAM_CLIENT_ID || !INSTAGRAM_CLIENT_SECRET || !INSTAGRAM_REDIRECT_URI) {
      return NextResponse.json(
        { error: 'Instagram API credentials not configured' },
        { status: 500 }
      );
    }

    // Get business ID from query parameters
    const searchParams = request.nextUrl.searchParams;
    const businessId = searchParams.get('businessId');

    if (!businessId) {
      return NextResponse.json(
        { error: 'Business ID is required' },
        { status: 400 }
      );
    }

    // Generate Instagram login URL with state parameter to store businessId
    const state = Buffer.from(JSON.stringify({ businessId })).toString('base64');
    const loginUrl = getInstagramLoginUrl(INSTAGRAM_CLIENT_ID, INSTAGRAM_REDIRECT_URI);
    const urlWithState = `${loginUrl}&state=${state}`;

    // Redirect to Instagram login
    return NextResponse.redirect(urlWithState);
  } catch (error: any) {
    console.error('Error initiating Instagram authentication:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
