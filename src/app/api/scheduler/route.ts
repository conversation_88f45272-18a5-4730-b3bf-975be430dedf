import { NextRequest, NextResponse } from 'next/server';
import { processScheduledPosts } from '../../../services/scheduler';

/**
 * POST handler for manually triggering the scheduler
 * This can be called by a cron job or manually for testing
 */
export async function POST(request: NextRequest) {
  try {
    // Check for API key (basic security)
    const apiKey = request.headers.get('x-api-key');
    const configuredApiKey = process.env.SCHEDULER_API_KEY;
    
    if (configuredApiKey && apiKey !== configuredApiKey) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Process scheduled posts
    await processScheduledPosts();

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Error processing scheduled posts:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
