import { NextRequest, NextResponse } from 'next/server'
import payload from 'payload'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')
    const business = searchParams.get('business')
    const platform = searchParams.get('platform')
    const status = searchParams.get('status')

    if (id) {
      // Get a specific social media account
      const account = await payload.findByID({
        collection: 'social-media-accounts',
        id,
        depth: 1,
      })

      return NextResponse.json(account)
    } else {
      // Build query based on parameters
      const query: any = {}

      if (business) {
        query.business = {
          equals: business,
        }
      }

      if (platform) {
        query.platform = {
          equals: platform,
        }
      }

      if (status) {
        query.status = {
          equals: status,
        }
      }

      // Get social media accounts based on query
      const accounts = await payload.find({
        collection: 'social-media-accounts',
        where: Object.keys(query).length > 0 ? { and: [query] } : {},
        depth: 1,
      })

      return NextResponse.json(accounts)
    }
  } catch (error: any) {
    console.error('Error fetching social media accounts:', error)
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if this is actually a GET request with method override
    const methodOverride = request.headers.get('x-http-method-override')
    if (methodOverride === 'GET') {
      // This is actually a GET request, delegate to GET handler
      return GET(request)
    }

    // Check content type to determine how to parse the request
    const contentType = request.headers.get('content-type') || ''

    let data

    if (contentType.includes('multipart/form-data')) {
      // Handle form data
      const formData = await request.formData()

      // Check if this is Payload CMS admin interface data
      const payloadData = formData.get('_payload')
      if (payloadData) {
        try {
          // Parse the JSON data from Payload CMS admin interface
          data = JSON.parse(payloadData as string)
        } catch (parseError) {
          console.error('Error parsing _payload field:', parseError)
          return NextResponse.json({ error: 'Invalid JSON in _payload field' }, { status: 400 })
        }
      } else {
        // Convert form data to object (for custom form submissions)
        data = {}
        for (const [key, value] of formData.entries()) {
          // Handle file uploads
          if (value instanceof File) {
            console.log(`File found in key ${key}:`, value.name, value.type, value.size)
            // For now, skip file handling - files should be uploaded separately
            continue
          }

          // Handle nested objects with dot notation
          if (key.includes('.')) {
            const [parent, child] = key.split('.')
            if (!data[parent]) data[parent] = {}
            data[parent][child] = value
          } else {
            data[key] = value
          }
        }
      }
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      // Handle URL-encoded form data
      try {
        const formData = await request.formData()
        data = {}
        for (const [key, value] of formData.entries()) {
          // Handle nested objects with dot notation
          if (key.includes('.')) {
            const [parent, child] = key.split('.')
            if (!data[parent]) data[parent] = {}
            data[parent][child] = value
          } else {
            data[key] = value
          }
        }
      } catch (urlEncodedError) {
        console.error('Error parsing URL-encoded data:', urlEncodedError)
        return NextResponse.json({ error: 'Invalid URL-encoded data' }, { status: 400 })
      }
    } else {
      // Handle JSON data
      try {
        data = await request.json()
      } catch (jsonError) {
        console.error('Error parsing JSON:', jsonError)
        return NextResponse.json({ error: 'Invalid JSON in request body' }, { status: 400 })
      }
    }

    // Create a new social media account
    const account = await payload.create({
      collection: 'social-media-accounts',
      data: data,
    })

    return NextResponse.json(account)
  } catch (error: any) {
    console.error('Error creating social media account:', error)
    return NextResponse.json({ error: error.message || 'An error occurred' }, { status: 500 })
  }
}
