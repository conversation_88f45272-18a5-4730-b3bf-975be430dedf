import type { CollectionConfig } from 'payload'

export const SocialMediaAccounts: CollectionConfig = {
  slug: 'social-media-accounts',
  admin: {
    useAsTitle: 'accountName',
    defaultColumns: ['accountName', 'platform', 'business', 'status'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'accountName',
      type: 'text',
      required: true,
      label: 'Account Name',
    },
    {
      name: 'business',
      type: 'relationship',
      relationTo: 'businesses',
      required: true,
      hasMany: false,
    },
    {
      name: 'platform',
      type: 'select',
      options: [
        { label: 'Instagram', value: 'instagram' },
        { label: 'Facebook', value: 'facebook' },
        { label: 'Twitter', value: 'twitter' },
        { label: 'LinkedIn', value: 'linkedin' },
        { label: 'YouTube', value: 'youtube' },
      ],
      required: true,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Connected', value: 'connected' },
        { label: 'Disconnected', value: 'disconnected' },
        { label: 'Error', value: 'error' },
      ],
      defaultValue: 'disconnected',
      required: true,
    },
    {
      name: 'username',
      type: 'text',
      label: 'Username',
    },
    {
      name: 'profileUrl',
      type: 'text',
      label: 'Profile URL',
    },
    {
      name: 'profilePicture',
      type: 'upload',
      relationTo: 'media',
      label: 'Profile Picture',
    },
    {
      name: 'authDetails',
      type: 'group',
      label: 'Authentication Details',
      admin: {
        condition: () => false, // Hide in admin UI for security
      },
      fields: [
        {
          name: 'accessToken',
          type: 'text',
          label: 'Access Token',
        },
        {
          name: 'refreshToken',
          type: 'text',
          label: 'Refresh Token',
        },
        {
          name: 'tokenExpiry',
          type: 'date',
          label: 'Token Expiry',
        },
        {
          name: 'userId',
          type: 'text',
          label: 'User ID',
        },
      ],
    },
    {
      name: 'instagramSpecific',
      type: 'group',
      label: 'Instagram Specific Details',
      admin: {
        condition: (data) => data?.platform === 'instagram',
      },
      fields: [
        {
          name: 'businessAccount',
          type: 'checkbox',
          label: 'Is Business Account',
          defaultValue: false,
        },
        {
          name: 'mediaCount',
          type: 'number',
          label: 'Media Count',
        },
        {
          name: 'followerCount',
          type: 'number',
          label: 'Follower Count',
        },
        {
          name: 'followingCount',
          type: 'number',
          label: 'Following Count',
        },
      ],
    },
    {
      name: 'lastSyncedAt',
      type: 'date',
      label: 'Last Synced At',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'lastSyncStatus',
      type: 'select',
      options: [
        { label: 'Success', value: 'success' },
        { label: 'Failed', value: 'failed' },
        { label: 'Never Synced', value: 'never' },
      ],
      defaultValue: 'never',
    },
  ],
}
