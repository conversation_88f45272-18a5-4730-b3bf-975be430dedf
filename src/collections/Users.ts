import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['email', 'name', 'role', 'createdAt'],
  },
  auth: true,
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          id: {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          id: {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    // Email added by default
    {
      name: 'name',
      type: 'text',
      label: 'Full Name',
    },
    {
      name: 'role',
      type: 'select',
      options: [
        { label: 'Admin', value: 'admin' },
        { label: 'User', value: 'user' },
      ],
      defaultValue: 'user',
      required: true,

    },
    {
      name: 'phone',
      type: 'text',
      label: 'Phone Number',
    },
    {
      name: 'profileImage',
      type: 'upload',
      relationTo: 'media',
      label: 'Profile Image',
    },
    {
      name: 'subscription',
      type: 'group',
      label: 'Subscription Details',
      fields: [
        {
          name: 'plan',
          type: 'select',
          options: [
            { label: 'Free', value: 'free' },
            { label: 'Basic', value: 'basic' },
            { label: 'Premium', value: 'premium' },
            { label: 'Enterprise', value: 'enterprise' },
          ],
          defaultValue: 'free',
        },
        {
          name: 'status',
          type: 'select',
          options: [
            { label: 'Active', value: 'active' },
            { label: 'Inactive', value: 'inactive' },
            { label: 'Trial', value: 'trial' },
            { label: 'Expired', value: 'expired' },
          ],
          defaultValue: 'active',
        },
        {
          name: 'startDate',
          type: 'date',
          label: 'Start Date',
        },
        {
          name: 'endDate',
          type: 'date',
          label: 'End Date',
        },
        {
          name: 'razorpayCustomerId',
          type: 'text',
          label: 'Razorpay Customer ID',
          admin: {
            condition: () => false, // Hide in admin UI
          },
        },
      ],
    },
  ],
}
