import type { CollectionConfig } from 'payload'
import {
  validateInstagramCaption,
  validateInstagramHashtags,
  INSTAGRAM_SUPPORTED_IMAGE_FORMATS,
  INSTAGRAM_SUPPORTED_VIDEO_FORMATS,
} from '../utils/instagram-validators'

export const SocialMediaContent: CollectionConfig = {
  slug: 'social-media-content',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'platform', 'status', 'scheduledDate'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Content Title',
    },
    {
      name: 'business',
      type: 'relationship',
      relationTo: 'businesses',
      required: true,
      hasMany: false,
    },

    {
      name: 'platform',
      type: 'select',
      options: [
        { label: 'Instagram', value: 'instagram' },
        { label: 'Facebook', value: 'facebook' },
        { label: 'Twitter', value: 'twitter' },
        { label: 'LinkedIn', value: 'linkedin' },
        { label: 'YouTube', value: 'youtube' },
      ],
      required: true,
    },
    {
      name: 'contentType',
      type: 'select',
      options: [
        { label: 'Image Post', value: 'image' },
        { label: 'Carousel', value: 'carousel' },
        { label: 'Video', value: 'video' },
        { label: 'Story', value: 'story' },
        { label: 'Text Only', value: 'text' },
      ],
      required: true,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Published', value: 'published' },
        { label: 'Failed', value: 'failed' },
      ],
      defaultValue: 'draft',
      required: true,
    },
    {
      name: 'caption',
      type: 'textarea',
      label: 'Post Caption',
      validate: (value, { data }: { data: any }) => {
        if (data?.platform === 'instagram') {
          const validation = validateInstagramCaption(value || '')
          if (!validation.valid && validation.message) {
            return validation.message
          }
        }
        return true
      },
      admin: {
        description: (args: any) => {
          const { value, data } = args
          if (data?.platform === 'instagram') {
            const remainingChars = 2200 - (value?.length || 0)
            return `${remainingChars} characters remaining (Instagram limit: 2200)`
          }
          return ''
        },
      },
    },
    {
      name: 'hashtags',
      type: 'array',
      label: 'Hashtags',
      validate: (value, { data }: { data: any }) => {
        if (data?.platform === 'instagram' && Array.isArray(value)) {
          const hashtags = value.map((item: any) => item?.tag || '')
          const validation = validateInstagramHashtags(hashtags)
          if (!validation.valid && validation.message) {
            return validation.message
          }
        }
        return true
      },
      admin: {
        description: (args: any) => {
          const { value, data } = args
          if (data?.platform === 'instagram') {
            const count = Array.isArray(value) ? value.length : 0
            return `${count}/30 hashtags (Instagram limit: 30)`
          }
          return ''
        },
      },
      fields: [
        {
          name: 'tag',
          type: 'text',
          validate: (value: any) => {
            // Allow empty values (user might not want to add hashtags)
            if (!value || value.trim() === '') {
              return true
            }

            if (typeof value === 'string' && !value.startsWith('#')) {
              return 'Hashtag should start with #'
            }
            return true
          },
        },
      ],
    },
    {
      name: 'visualDescription',
      type: 'textarea',
      label: 'Visual Description',
      admin: {
        description:
          'Describe the visual elements, composition, colors, and overall aesthetic of the content',
        placeholder:
          'e.g., A bright, minimalist product shot with soft natural lighting, featuring the product centered on a white background with subtle shadows...',
      },
    },
    {
      name: 'media',
      type: 'array',
      label: 'Media Files',
      validate: (value, { data }: { data: any }) => {
        if (data?.platform === 'instagram') {
          // Only require media for non-text content types
          if (data?.contentType !== 'text' && (!value || value.length === 0)) {
            return 'Instagram posts require at least one media file'
          }

          if (data?.contentType === 'carousel' && value && value.length > 10) {
            return 'Instagram carousel posts can have a maximum of 10 images'
          }
        }
        return true
      },
      admin: {
        description: (args: any) => {
          const { data } = args
          if (data?.platform === 'instagram') {
            if (data?.contentType === 'carousel') {
              return 'Instagram carousel: 2-10 images with the same aspect ratio'
            } else if (data?.contentType === 'image') {
              return 'Instagram image: Aspect ratio between 4:5 and 1.91:1'
            } else if (data?.contentType === 'video') {
              return 'Instagram video: 3-60 seconds, MP4 format'
            }
          }
          return ''
        },
      },
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: false, // Made optional to allow flexibility
          filterOptions: {
            mimeType: {
              in: [...INSTAGRAM_SUPPORTED_IMAGE_FORMATS, ...INSTAGRAM_SUPPORTED_VIDEO_FORMATS],
            },
          },
        },
        {
          name: 'altText',
          type: 'text',
          label: 'Alt Text',
        },
        {
          name: 'mediaValidation',
          type: 'group',
          label: 'Media Validation',
          admin: {
            condition: () => false, // Hide in admin UI
          },
          fields: [
            {
              name: 'width',
              type: 'number',
              label: 'Width',
            },
            {
              name: 'height',
              type: 'number',
              label: 'Height',
            },
            {
              name: 'aspectRatio',
              type: 'number',
              label: 'Aspect Ratio',
            },
            {
              name: 'duration',
              type: 'number',
              label: 'Duration (seconds)',
            },
          ],
        },
      ],
    },
    {
      name: 'scheduledDate',
      type: 'date',
      label: 'Scheduled Date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'publishedDate',
      type: 'date',
      label: 'Published Date',
      admin: {
        readOnly: true,
        date: {
          pickerAppearance: 'dayAndTime',
        },
      },
    },
    {
      name: 'analytics',
      type: 'group',
      label: 'Post Analytics',
      fields: [
        {
          name: 'likes',
          type: 'number',
          label: 'Likes',
          defaultValue: 0,
        },
        {
          name: 'comments',
          type: 'number',
          label: 'Comments',
          defaultValue: 0,
        },
        {
          name: 'shares',
          type: 'number',
          label: 'Shares',
          defaultValue: 0,
        },
        {
          name: 'impressions',
          type: 'number',
          label: 'Impressions',
          defaultValue: 0,
        },
        {
          name: 'reach',
          type: 'number',
          label: 'Reach',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'aiGenerated',
      type: 'checkbox',
      label: 'AI Generated Content',
      defaultValue: false,
    },
    {
      name: 'generationPrompt',
      type: 'textarea',
      label: 'Generation Prompt',
      admin: {
        condition: (data) => data?.aiGenerated === true,
      },
    },
    {
      name: 'postingDetails',
      type: 'group',
      label: 'Posting Details',
      admin: {
        condition: (data) => ['scheduled', 'published', 'failed'].includes(data?.status),
      },
      fields: [
        {
          name: 'postId',
          type: 'text',
          label: 'Platform Post ID',
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'postUrl',
          type: 'text',
          label: 'Post URL',
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'attempts',
          type: 'number',
          label: 'Posting Attempts',
          defaultValue: 0,
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'lastAttemptAt',
          type: 'date',
          label: 'Last Attempt At',
          admin: {
            readOnly: true,
            date: {
              pickerAppearance: 'dayAndTime',
            },
          },
        },
        {
          name: 'error',
          type: 'textarea',
          label: 'Error Message',
          admin: {
            readOnly: true,
            condition: (data) => data?.status === 'failed',
          },
        },
      ],
    },
    {
      name: 'instagramSpecific',
      type: 'group',
      label: 'Instagram Specific Settings',
      admin: {
        condition: (data) => data?.platform === 'instagram',
      },
      fields: [
        {
          name: 'firstComment',
          type: 'textarea',
          label: 'First Comment',
          admin: {
            description: 'Add a comment that will be posted immediately after publishing',
          },
        },
        {
          name: 'locationName',
          type: 'text',
          label: 'Location Name',
        },
        {
          name: 'userTags',
          type: 'array',
          label: 'Tag Users',
          fields: [
            {
              name: 'username',
              type: 'text',
              required: true,
            },
            {
              name: 'x',
              type: 'number',
              label: 'X Position (0-1)',
              min: 0,
              max: 1,
              defaultValue: 0.5,
            },
            {
              name: 'y',
              type: 'number',
              label: 'Y Position (0-1)',
              min: 0,
              max: 1,
              defaultValue: 0.5,
            },
          ],
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Set default status based on scheduledDate
        if (operation === 'create' && data.scheduledDate) {
          const scheduledDate = new Date(data.scheduledDate)
          const now = new Date()

          if (scheduledDate > now) {
            data.status = 'scheduled'
          }
        }

        return data
      },
    ],
  },
}
