import type { CollectionConfig } from 'payload'

export const Businesses: CollectionConfig = {
  slug: 'businesses',
  admin: {
    useAsTitle: 'businessName',
    defaultColumns: ['businessName', 'industry', 'createdAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          owner: {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          owner: {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          owner: {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'businessName',
      type: 'text',
      required: true,
      label: 'Business Name',
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      label: 'Business Logo',
      filterOptions: {
        mimeType: { contains: 'image' },
      },
    },
    {
      name: 'brandBrief',
      type: 'textarea',
      label: 'Brand Brief',
      maxLength: 1000,
      admin: {
        description: 'Describe your brand, values, and target audience (max 1000 characters)',
      },
    },
    {
      name: 'industry',
      type: 'select',
      options: [
        { label: 'Retail', value: 'retail' },
        { label: 'Food & Beverage', value: 'food_beverage' },
        { label: 'Technology', value: 'technology' },
        { label: 'Healthcare', value: 'healthcare' },
        { label: 'Education', value: 'education' },
        { label: 'Professional Services', value: 'professional_services' },
        { label: 'Manufacturing', value: 'manufacturing' },
        { label: 'Construction', value: 'construction' },
        { label: 'Transportation', value: 'transportation' },
        { label: 'Hospitality', value: 'hospitality' },
        { label: 'Other', value: 'other' },
      ],
      required: true,
    },
    {
      name: 'location',
      type: 'group',
      fields: [
        {
          name: 'address',
          type: 'text',
          label: 'Address',
        },
        {
          name: 'city',
          type: 'text',
          label: 'City',
        },
        {
          name: 'state',
          type: 'text',
          label: 'State',
        },
        {
          name: 'pincode',
          type: 'text',
          label: 'PIN Code',
        },
      ],
    },
    {
      name: 'contactInfo',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'email',
          label: 'Business Email',
        },
        {
          name: 'phone',
          type: 'text',
          label: 'Business Phone',
        },
        {
          name: 'website',
          type: 'text',
          label: 'Current Website (if any)',
        },
      ],
    },
    {
      name: 'brandElements',
      type: 'group',
      label: 'AI-Generated Brand Elements',
      fields: [
        {
          name: 'colorPalette',
          type: 'array',
          label: 'Color Palette',
          fields: [
            {
              name: 'colorHex',
              type: 'text',
              label: 'Color (Hex)',
            },
            {
              name: 'colorName',
              type: 'text',
              label: 'Color Name',
            },
          ],
        },
        {
          name: 'seoKeywords',
          type: 'array',
          label: 'SEO Keywords',
          fields: [
            {
              name: 'keyword',
              type: 'text',
            },
            {
              name: 'relevanceScore',
              type: 'number',
              min: 0,
              max: 100,
            },
          ],
        },
        {
          name: 'competitors',
          type: 'array',
          label: 'Identified Competitors',
          fields: [
            {
              name: 'name',
              type: 'text',
              label: 'Competitor Name',
            },
            {
              name: 'website',
              type: 'text',
              label: 'Website',
            },
          ],
        },
      ],
    },
    {
      name: 'owner',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      hasMany: false,
      admin: {
        condition: () => false,
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, data }) => {
        if (req.user) {
          return {
            ...data,
            owner: req.user.id,
          }
        }
        return data
      },
    ],
  },
}
