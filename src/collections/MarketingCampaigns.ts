import type { CollectionConfig } from 'payload'

export const MarketingCampaigns: CollectionConfig = {
  slug: 'marketing-campaigns',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'business', 'type', 'status', 'startDate'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Campaign Name',
    },
    {
      name: 'business',
      type: 'relationship',
      relationTo: 'businesses',
      required: true,
      hasMany: false,
    },
    {
      name: 'type',
      type: 'select',
      options: [
        { label: 'SEO', value: 'seo' },
        { label: 'Social Media', value: 'social_media' },
        { label: 'Email', value: 'email' },
        { label: 'Google Ads', value: 'google_ads' },
        { label: 'Facebook Ads', value: 'facebook_ads' },
        { label: 'Content Marketing', value: 'content_marketing' },
        { label: 'Local SEO', value: 'local_seo' },
      ],
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Campaign Description',
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Active', value: 'active' },
        { label: 'Paused', value: 'paused' },
        { label: 'Completed', value: 'completed' },
      ],
      defaultValue: 'draft',
      required: true,
    },
    {
      name: 'startDate',
      type: 'date',
      label: 'Start Date',
    },
    {
      name: 'endDate',
      type: 'date',
      label: 'End Date',
    },
    {
      name: 'budget',
      type: 'number',
      label: 'Budget (INR)',
      min: 0,
    },
    {
      name: 'targetAudience',
      type: 'group',
      label: 'Target Audience',
      fields: [
        {
          name: 'demographics',
          type: 'group',
          fields: [
            {
              name: 'ageRange',
              type: 'select',
              options: [
                { label: 'All Ages', value: 'all' },
                { label: '18-24', value: '18-24' },
                { label: '25-34', value: '25-34' },
                { label: '35-44', value: '35-44' },
                { label: '45-54', value: '45-54' },
                { label: '55+', value: '55+' },
              ],
              defaultValue: 'all',
            },
            {
              name: 'gender',
              type: 'select',
              options: [
                { label: 'All', value: 'all' },
                { label: 'Male', value: 'male' },
                { label: 'Female', value: 'female' },
              ],
              defaultValue: 'all',
            },
            {
              name: 'locations',
              type: 'array',
              fields: [
                {
                  name: 'location',
                  type: 'text',
                  label: 'Location (City/State)',
                },
              ],
            },
          ],
        },
        {
          name: 'interests',
          type: 'array',
          fields: [
            {
              name: 'interest',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      name: 'keywords',
      type: 'array',
      label: 'Target Keywords',
      admin: {
        condition: (data) => ['seo', 'google_ads', 'local_seo', 'content_marketing'].includes(data?.type),
      },
      fields: [
        {
          name: 'keyword',
          type: 'text',
          required: true,
        },
        {
          name: 'difficulty',
          type: 'select',
          options: [
            { label: 'Easy', value: 'easy' },
            { label: 'Medium', value: 'medium' },
            { label: 'Hard', value: 'hard' },
          ],
        },
        {
          name: 'searchVolume',
          type: 'number',
          label: 'Monthly Search Volume',
        },
      ],
    },
    {
      name: 'googleBusinessProfile',
      type: 'group',
      label: 'Google Business Profile',
      admin: {
        condition: (data) => data?.type === 'local_seo',
      },
      fields: [
        {
          name: 'isConnected',
          type: 'checkbox',
          label: 'Profile Connected',
          defaultValue: false,
        },
        {
          name: 'profileUrl',
          type: 'text',
          label: 'Profile URL',
        },
        {
          name: 'lastUpdated',
          type: 'date',
          label: 'Last Updated',
        },
      ],
    },
    {
      name: 'content',
      type: 'array',
      label: 'Campaign Content',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'contentType',
          type: 'select',
          options: [
            { label: 'Blog Post', value: 'blog' },
            { label: 'Social Media Post', value: 'social' },
            { label: 'Ad Copy', value: 'ad' },
            { label: 'Email', value: 'email' },
            { label: 'Landing Page', value: 'landing_page' },
          ],
          required: true,
        },
        {
          name: 'status',
          type: 'select',
          options: [
            { label: 'Draft', value: 'draft' },
            { label: 'Published', value: 'published' },
            { label: 'Scheduled', value: 'scheduled' },
          ],
          defaultValue: 'draft',
        },
        {
          name: 'media',
          type: 'upload',
          relationTo: 'media',
          label: 'Media',
        },
      ],
    },
    {
      name: 'analytics',
      type: 'group',
      label: 'Campaign Analytics',
      fields: [
        {
          name: 'impressions',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'clicks',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'conversions',
          type: 'number',
          defaultValue: 0,
        },
        {
          name: 'spend',
          type: 'number',
          label: 'Amount Spent (INR)',
          defaultValue: 0,
        },
        {
          name: 'roi',
          type: 'number',
          label: 'ROI (%)',
          defaultValue: 0,
        },
      ],
    },
    {
      name: 'aiGenerated',
      type: 'checkbox',
      label: 'AI Generated Campaign',
      defaultValue: false,
    },
  ],
}
