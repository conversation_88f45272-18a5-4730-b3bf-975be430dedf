import type { CollectionConfig } from 'payload'

export const YouTubeVideos: CollectionConfig = {
  slug: 'youtube-videos',
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'business', 'status', 'publishedDate'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      label: 'Video Title',
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Video Description',
    },
    {
      name: 'business',
      type: 'relationship',
      relationTo: 'businesses',
      required: true,
      hasMany: false,
    },
    {
      name: 'youtubeAccount',
      type: 'relationship',
      relationTo: 'social-media-accounts',
      required: true,
      hasMany: false,
      admin: {
        description: 'Select the YouTube account to use',
      },
      filterOptions: {
        platform: {
          equals: 'youtube',
        },
        status: {
          equals: 'connected',
        },
      },
    },
    {
      name: 'videoId',
      type: 'text',
      label: 'YouTube Video ID',
      admin: {
        description: 'The unique identifier for the video on YouTube',
      },
    },
    {
      name: 'videoUrl',
      type: 'text',
      label: 'YouTube Video URL',
      admin: {
        description: 'Full URL to the video on YouTube',
      },
    },
    {
      name: 'thumbnailUrl',
      type: 'text',
      label: 'Thumbnail URL',
    },
    {
      name: 'customThumbnail',
      type: 'upload',
      relationTo: 'media',
      label: 'Custom Thumbnail',
      admin: {
        description: 'Upload a custom thumbnail for this video',
      },
      filterOptions: {
        mimeType: { contains: 'image' },
      },
    },
    {
      name: 'videoFile',
      type: 'upload',
      relationTo: 'media',
      label: 'Video File',
      admin: {
        description: 'Upload the video file (for videos to be uploaded to YouTube)',
      },
      filterOptions: {
        mimeType: { contains: 'video' },
      },
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Scheduled', value: 'scheduled' },
        { label: 'Published', value: 'published' },
        { label: 'Failed', value: 'failed' },
      ],
      defaultValue: 'draft',
      required: true,
    },
    {
      name: 'visibility',
      type: 'select',
      options: [
        { label: 'Public', value: 'public' },
        { label: 'Unlisted', value: 'unlisted' },
        { label: 'Private', value: 'private' },
      ],
      defaultValue: 'public',
      required: true,
    },
    {
      name: 'publishedDate',
      type: 'date',
      label: 'Published Date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When the video was or will be published on YouTube',
      },
    },
    {
      name: 'scheduledDate',
      type: 'date',
      label: 'Scheduled Date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When to publish this video to YouTube',
      },
    },
    {
      name: 'category',
      type: 'select',
      options: [
        { label: 'Film & Animation', value: 'film_animation' },
        { label: 'Autos & Vehicles', value: 'autos_vehicles' },
        { label: 'Music', value: 'music' },
        { label: 'Pets & Animals', value: 'pets_animals' },
        { label: 'Sports', value: 'sports' },
        { label: 'Travel & Events', value: 'travel_events' },
        { label: 'Gaming', value: 'gaming' },
        { label: 'People & Blogs', value: 'people_blogs' },
        { label: 'Comedy', value: 'comedy' },
        { label: 'Entertainment', value: 'entertainment' },
        { label: 'News & Politics', value: 'news_politics' },
        { label: 'Howto & Style', value: 'howto_style' },
        { label: 'Education', value: 'education' },
        { label: 'Science & Technology', value: 'science_technology' },
        { label: 'Nonprofits & Activism', value: 'nonprofits_activism' },
      ],
    },
    {
      name: 'tags',
      type: 'array',
      label: 'Tags',
      admin: {
        description: 'Tags to help viewers find your video',
      },
      fields: [
        {
          name: 'tag',
          type: 'text',
        },
      ],
    },
    {
      name: 'statistics',
      type: 'group',
      label: 'Video Statistics',
      admin: {
        description: 'Statistics from YouTube (automatically updated)',
      },
      fields: [
        {
          name: 'views',
          type: 'number',
          label: 'Views',
        },
        {
          name: 'likes',
          type: 'number',
          label: 'Likes',
        },
        {
          name: 'comments',
          type: 'number',
          label: 'Comments',
        },
        {
          name: 'lastUpdated',
          type: 'date',
          label: 'Last Updated',
        },
      ],
    },
    {
      name: 'uploadStatus',
      type: 'group',
      label: 'Upload Status',
      admin: {
        description: 'Information about the upload process',
      },
      fields: [
        {
          name: 'progress',
          type: 'number',
          label: 'Upload Progress',
          min: 0,
          max: 100,
        },
        {
          name: 'message',
          type: 'text',
          label: 'Status Message',
        },
        {
          name: 'error',
          type: 'text',
          label: 'Error Message',
        },
        {
          name: 'retryCount',
          type: 'number',
          label: 'Retry Count',
          defaultValue: 0,
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation }) => {
        // Set default status based on scheduledDate
        if (operation === 'create' && data.scheduledDate) {
          const scheduledDate = new Date(data.scheduledDate);
          const now = new Date();

          if (scheduledDate > now) {
            data.status = 'scheduled';
          }
        }

        return data;
      },
    ],
  },
}
