import type { CollectionConfig } from 'payload'

export const Websites: CollectionConfig = {
  slug: 'websites',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'business', 'status', 'createdAt'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Website Name',
    },
    {
      name: 'business',
      type: 'relationship',
      relationTo: 'businesses',
      required: true,
      hasMany: false,
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
      required: true,
    },
    {
      name: 'template',
      type: 'select',
      options: [
        { label: 'E-commerce', value: 'ecommerce' },
        { label: 'Business', value: 'business' },
        { label: 'Portfolio', value: 'portfolio' },
        { label: 'Blog', value: 'blog' },
        { label: 'Restaurant', value: 'restaurant' },
        { label: 'Service Business', value: 'service' },
      ],
      required: true,
    },
    {
      name: 'domain',
      type: 'group',
      fields: [
        {
          name: 'customDomain',
          type: 'text',
          label: 'Custom Domain',
        },
        {
          name: 'subdomain',
          type: 'text',
          label: 'Subdomain',
          admin: {
            description: 'Your site will be available at [subdomain].dukanify.com',
          },
        },
        {
          name: 'isConnected',
          type: 'checkbox',
          label: 'Domain Connected',
          defaultValue: false,
        },
      ],
    },
    {
      name: 'pages',
      type: 'array',
      label: 'Website Pages',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'slug',
          type: 'text',
          required: true,
        },
        {
          name: 'layout',
          type: 'json',
          label: 'Page Layout',
        },
        {
          name: 'seo',
          type: 'group',
          label: 'SEO Settings',
          fields: [
            {
              name: 'title',
              type: 'text',
              label: 'SEO Title',
            },
            {
              name: 'description',
              type: 'textarea',
              label: 'Meta Description',
            },
            {
              name: 'keywords',
              type: 'text',
              label: 'Keywords',
            },
          ],
        },
      ],
    },
    {
      name: 'products',
      type: 'array',
      label: 'Products (for E-commerce)',
      admin: {
        condition: (data) => data?.template === 'ecommerce',
      },
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
        },
        {
          name: 'price',
          type: 'number',
          required: true,
        },
        {
          name: 'images',
          type: 'array',
          fields: [
            {
              name: 'image',
              type: 'upload',
              relationTo: 'media',
              required: true,
            },
          ],
        },
      ],
    },
    {
      name: 'analytics',
      type: 'group',
      fields: [
        {
          name: 'googleAnalyticsId',
          type: 'text',
          label: 'Google Analytics ID',
        },
        {
          name: 'facebookPixelId',
          type: 'text',
          label: 'Facebook Pixel ID',
        },
      ],
    },
  ],
}
