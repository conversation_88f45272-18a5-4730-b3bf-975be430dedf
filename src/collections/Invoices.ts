import type { CollectionConfig } from 'payload'

export const Invoices: CollectionConfig = {
  slug: 'invoices',
  admin: {
    useAsTitle: 'invoiceNumber',
    defaultColumns: ['invoiceNumber', 'business', 'customer', 'totalAmount', 'status'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    update: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
    delete: ({ req: { user } }) => {
      if (user) {
        if (user.role === 'admin') return true
        return {
          'business.owner': {
            equals: user.id,
          },
        }
      }
      return false
    },
  },
  fields: [
    {
      name: 'invoiceNumber',
      type: 'text',
      required: true,
      label: 'Invoice Number',
    },
    {
      name: 'business',
      type: 'relationship',
      relationTo: 'businesses',
      required: true,
      hasMany: false,
    },
    {
      name: 'customer',
      type: 'group',
      label: 'Customer Information',
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
          label: 'Customer Name',
        },
        {
          name: 'email',
          type: 'email',
          label: 'Customer Email',
        },
        {
          name: 'phone',
          type: 'text',
          label: 'Customer Phone',
        },
        {
          name: 'address',
          type: 'textarea',
          label: 'Customer Address',
        },
        {
          name: 'gstin',
          type: 'text',
          label: 'Customer GSTIN',
        },
      ],
    },
    {
      name: 'invoiceDate',
      type: 'date',
      required: true,
      label: 'Invoice Date',
    },
    {
      name: 'dueDate',
      type: 'date',
      label: 'Due Date',
    },
    {
      name: 'items',
      type: 'array',
      label: 'Invoice Items',
      required: true,
      fields: [
        {
          name: 'description',
          type: 'text',
          required: true,
          label: 'Item Description',
        },
        {
          name: 'quantity',
          type: 'number',
          required: true,
          min: 1,
          defaultValue: 1,
        },
        {
          name: 'unitPrice',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'hsnSac',
          type: 'text',
          label: 'HSN/SAC Code',
        },
        {
          name: 'taxRate',
          type: 'select',
          options: [
            { label: 'None', value: '0' },
            { label: 'GST 5%', value: '5' },
            { label: 'GST 12%', value: '12' },
            { label: 'GST 18%', value: '18' },
            { label: 'GST 28%', value: '28' },
          ],
          defaultValue: '18',
          required: true,
        },
        {
          name: 'taxAmount',
          type: 'number',
          label: 'Tax Amount',
          admin: {
            readOnly: true,
          },
        },
        {
          name: 'totalAmount',
          type: 'number',
          label: 'Total Amount',
          admin: {
            readOnly: true,
          },
        },
      ],
    },
    {
      name: 'subtotal',
      type: 'number',
      label: 'Subtotal (before tax)',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'taxTotal',
      type: 'number',
      label: 'Total Tax',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'totalAmount',
      type: 'number',
      label: 'Total Amount',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      label: 'Invoice Notes',
    },
    {
      name: 'termsAndConditions',
      type: 'textarea',
      label: 'Terms and Conditions',
    },
    {
      name: 'status',
      type: 'select',
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Sent', value: 'sent' },
        { label: 'Paid', value: 'paid' },
        { label: 'Overdue', value: 'overdue' },
        { label: 'Cancelled', value: 'cancelled' },
      ],
      defaultValue: 'draft',
      required: true,
    },
    {
      name: 'paymentDetails',
      type: 'group',
      label: 'Payment Details',
      fields: [
        {
          name: 'method',
          type: 'select',
          options: [
            { label: 'Cash', value: 'cash' },
            { label: 'Bank Transfer', value: 'bank_transfer' },
            { label: 'UPI', value: 'upi' },
            { label: 'Credit Card', value: 'credit_card' },
            { label: 'Debit Card', value: 'debit_card' },
            { label: 'Cheque', value: 'cheque' },
          ],
        },
        {
          name: 'transactionId',
          type: 'text',
          label: 'Transaction ID',
        },
        {
          name: 'paidDate',
          type: 'date',
          label: 'Date Paid',
        },
        {
          name: 'paidAmount',
          type: 'number',
          label: 'Amount Paid',
        },
      ],
    },
    {
      name: 'aiGenerated',
      type: 'checkbox',
      label: 'AI Generated Invoice',
      defaultValue: false,
    },
  ],
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Calculate tax and totals for each item
        if (data.items && Array.isArray(data.items)) {
          let subtotal = 0
          let taxTotal = 0
          
          data.items = data.items.map(item => {
            const quantity = item.quantity || 1
            const unitPrice = item.unitPrice || 0
            const taxRate = parseFloat(item.taxRate || '0')
            
            const itemSubtotal = quantity * unitPrice
            const itemTaxAmount = (itemSubtotal * taxRate) / 100
            const itemTotal = itemSubtotal + itemTaxAmount
            
            subtotal += itemSubtotal
            taxTotal += itemTaxAmount
            
            return {
              ...item,
              taxAmount: itemTaxAmount,
              totalAmount: itemTotal,
            }
          })
          
          data.subtotal = subtotal
          data.taxTotal = taxTotal
          data.totalAmount = subtotal + taxTotal
        }
        
        return data
      },
    ],
  },
}
