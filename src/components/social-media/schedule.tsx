'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Instagram,
  Facebook,
  Twitter,
  Linkedin,
  Calendar,
  Clock,
  AlertCircle,
  MoreHorizontal,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

type Business = {
  id: string
  businessName: string
}

type ScheduledPost = {
  id: string
  title: string
  platform: string
  status: string
  scheduledDate: string
  caption: string
  media: Array<{ file: { url: string } }>
  business: {
    businessName: string
  }
}

export default function SocialMediaSchedule() {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [selectedBusiness, setSelectedBusiness] = useState<string>('')
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledPost[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPost, setSelectedPost] = useState<ScheduledPost | null>(null)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)

  useEffect(() => {
    // Fetch businesses
    const fetchBusinesses = async () => {
      try {
        const response = await fetch('/api/businesses')
        const data = await response.json()

        if (data.docs && data.docs.length > 0) {
          setBusinesses(data.docs)
          setSelectedBusiness(data.docs[0].id)
        }
      } catch (err) {
        console.error('Error fetching businesses:', err)
        setError('Failed to load businesses')
      }
    }

    fetchBusinesses()
  }, [])

  useEffect(() => {
    // Fetch scheduled posts when a business is selected
    const fetchScheduledPosts = async () => {
      if (!selectedBusiness) return

      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(
          `/api/social-media-content?business=${selectedBusiness}&status=scheduled`,
        )
        const data = await response.json()
        setScheduledPosts(data.docs || [])
      } catch (err) {
        console.error('Error fetching scheduled posts:', err)
        setError('Failed to load scheduled posts')
      } finally {
        setIsLoading(false)
      }
    }

    fetchScheduledPosts()
  }, [selectedBusiness])

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return <Instagram className="h-5 w-5 text-pink-500" />
      case 'facebook':
        return <Facebook className="h-5 w-5 text-blue-600" />
      case 'twitter':
        return <Twitter className="h-5 w-5 text-blue-400" />
      case 'linkedin':
        return <Linkedin className="h-5 w-5 text-blue-700" />
      default:
        return null
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleViewPost = (post: ScheduledPost) => {
    setSelectedPost(post)
    setIsViewDialogOpen(true)
  }

  const handleDeletePost = async (postId: string) => {
    if (!confirm('Are you sure you want to delete this scheduled post?')) {
      return
    }

    try {
      const response = await fetch(`/api/social-media-content/${postId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete post')
      }

      // Remove the deleted post from the list
      setScheduledPosts(scheduledPosts.filter((post) => post.id !== postId))
    } catch (err) {
      console.error('Error deleting post:', err)
      setError('Failed to delete post')
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <label htmlFor="business-select" className="text-sm font-medium">
            Select Business:
          </label>
          <Select value={selectedBusiness} onValueChange={setSelectedBusiness}>
            <SelectTrigger className="w-[200px]" id="business-select">
              <SelectValue placeholder="Select a business" />
            </SelectTrigger>
            <SelectContent>
              {businesses.map((business) => (
                <SelectItem key={business.id} value={business.id}>
                  {business.businessName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 p-4 rounded-md flex items-center gap-2 text-red-800">
          <AlertCircle className="h-5 w-5" />
          <p>{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-10">Loading scheduled posts...</div>
      ) : scheduledPosts.length === 0 ? (
        <div className="text-center py-10 border rounded-md bg-gray-50">
          <p className="text-gray-500">No scheduled posts found.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {scheduledPosts.map((post) => (
            <Card key={post.id} className="overflow-hidden">
              <div className="relative h-40 bg-gray-100">
                {post.media && post.media[0] && (
                  <img
                    src={post.media[0].file.url}
                    alt={post.title}
                    className="w-full h-full object-cover"
                  />
                )}
                <div className="absolute top-2 right-2 bg-white rounded-full p-1">
                  {getPlatformIcon(post.platform)}
                </div>
              </div>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <h3 className="font-medium truncate">{post.title}</h3>
                  <div className="flex items-center gap-1 text-xs text-gray-500">
                    <Calendar className="h-3 w-3" />
                    <span>{formatDate(post.scheduledDate)}</span>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>{formatTime(post.scheduledDate)}</span>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <p className="text-sm text-gray-600 line-clamp-2">{post.caption}</p>
                <p className="text-xs text-gray-500 mt-1">
                  Business: {post.business?.businessName || 'Unknown'}
                </p>
              </CardContent>
              <CardFooter className="pt-0 flex justify-between">
                <Button variant="outline" size="sm" onClick={() => handleViewPost(post)}>
                  View Details
                </Button>
                <Button variant="ghost" size="icon" onClick={() => handleDeletePost(post.id)}>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* View Post Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedPost?.title}</DialogTitle>
            <DialogDescription>
              Scheduled for {selectedPost && formatDate(selectedPost.scheduledDate)} at{' '}
              {selectedPost && formatTime(selectedPost.scheduledDate)}
            </DialogDescription>
          </DialogHeader>

          {selectedPost && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {getPlatformIcon(selectedPost.platform)}
                <span className="text-sm">{selectedPost.business?.businessName}</span>
              </div>

              {selectedPost.media && selectedPost.media[0] && (
                <img
                  src={selectedPost.media[0].file.url}
                  alt={selectedPost.title}
                  className="w-full rounded-md"
                />
              )}

              <p className="text-sm whitespace-pre-line">{selectedPost.caption}</p>

              <div className="flex justify-between">
                <Button variant="outline" size="sm" onClick={() => setIsViewDialogOpen(false)}>
                  Close
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => {
                    setIsViewDialogOpen(false)
                    if (selectedPost) handleDeletePost(selectedPost.id)
                  }}
                >
                  Delete Post
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
