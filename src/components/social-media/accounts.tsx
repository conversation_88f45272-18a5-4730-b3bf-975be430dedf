"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Instagram, Facebook, Twitter, Linkedin, AlertCircle } from "lucide-react"
import { useRouter } from "next/navigation"

type SocialMediaAccount = {
  id: string
  accountName: string
  platform: string
  status: string
  username: string
  profileUrl: string
  lastSyncedAt: string
}

type Business = {
  id: string
  businessName: string
}

export default function SocialMediaAccounts() {
  const [accounts, setAccounts] = useState<SocialMediaAccount[]>([])
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [selectedBusiness, setSelectedBusiness] = useState<string>("")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  useEffect(() => {
    // Fetch businesses
    const fetchBusinesses = async () => {
      try {
        const response = await fetch('/api/businesses')
        const data = await response.json()
        
        if (data.docs && data.docs.length > 0) {
          setBusinesses(data.docs)
          setSelectedBusiness(data.docs[0].id)
        }
      } catch (err) {
        console.error('Error fetching businesses:', err)
        setError('Failed to load businesses')
      }
    }

    fetchBusinesses()
  }, [])

  useEffect(() => {
    // Fetch social media accounts when a business is selected
    const fetchAccounts = async () => {
      if (!selectedBusiness) return
      
      setIsLoading(true)
      setError(null)
      
      try {
        const response = await fetch(`/api/social-media-accounts?business=${selectedBusiness}`)
        const data = await response.json()
        setAccounts(data.docs || [])
      } catch (err) {
        console.error('Error fetching social media accounts:', err)
        setError('Failed to load social media accounts')
      } finally {
        setIsLoading(false)
      }
    }

    fetchAccounts()
  }, [selectedBusiness])

  const handleConnectInstagram = () => {
    if (!selectedBusiness) return
    window.location.href = `/api/instagram/auth?businessId=${selectedBusiness}`
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return <Instagram className="h-5 w-5 text-pink-500" />
      case 'facebook':
        return <Facebook className="h-5 w-5 text-blue-600" />
      case 'twitter':
        return <Twitter className="h-5 w-5 text-blue-400" />
      case 'linkedin':
        return <Linkedin className="h-5 w-5 text-blue-700" />
      default:
        return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Connected</span>
      case 'disconnected':
        return <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">Disconnected</span>
      case 'error':
        return <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Error</span>
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <label htmlFor="business-select" className="text-sm font-medium">
            Select Business:
          </label>
          <Select value={selectedBusiness} onValueChange={setSelectedBusiness}>
            <SelectTrigger className="w-[200px]" id="business-select">
              <SelectValue placeholder="Select a business" />
            </SelectTrigger>
            <SelectContent>
              {businesses.map((business) => (
                <SelectItem key={business.id} value={business.id}>
                  {business.businessName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <Dialog>
          <DialogTrigger asChild>
            <Button>Connect New Account</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Connect Social Media Account</DialogTitle>
              <DialogDescription>
                Choose a platform to connect to your business.
              </DialogDescription>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4 py-4">
              <Button 
                className="flex items-center justify-center gap-2 h-20" 
                onClick={handleConnectInstagram}
              >
                <Instagram className="h-6 w-6" />
                <span>Instagram</span>
              </Button>
              <Button 
                className="flex items-center justify-center gap-2 h-20" 
                variant="outline" 
                disabled
              >
                <Facebook className="h-6 w-6" />
                <span>Facebook</span>
                <span className="text-xs">(Coming Soon)</span>
              </Button>
              <Button 
                className="flex items-center justify-center gap-2 h-20" 
                variant="outline" 
                disabled
              >
                <Twitter className="h-6 w-6" />
                <span>Twitter</span>
                <span className="text-xs">(Coming Soon)</span>
              </Button>
              <Button 
                className="flex items-center justify-center gap-2 h-20" 
                variant="outline" 
                disabled
              >
                <Linkedin className="h-6 w-6" />
                <span>LinkedIn</span>
                <span className="text-xs">(Coming Soon)</span>
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <div className="bg-red-50 p-4 rounded-md flex items-center gap-2 text-red-800">
          <AlertCircle className="h-5 w-5" />
          <p>{error}</p>
        </div>
      )}

      {isLoading ? (
        <div className="text-center py-10">Loading accounts...</div>
      ) : accounts.length === 0 ? (
        <div className="text-center py-10 border rounded-md bg-gray-50">
          <p className="text-gray-500 mb-4">No social media accounts connected yet.</p>
          <Button onClick={() => document.querySelector<HTMLButtonElement>('[role="dialog"] button')?.click()}>
            Connect Your First Account
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {accounts.map((account) => (
            <Card key={account.id}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  {getPlatformIcon(account.platform)}
                  <CardTitle className="text-xl">{account.accountName}</CardTitle>
                </div>
                {getStatusBadge(account.status)}
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">Username: @{account.username}</p>
                  <p className="text-sm text-gray-500">
                    Last synced: {account.lastSyncedAt ? new Date(account.lastSyncedAt).toLocaleString() : 'Never'}
                  </p>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" size="sm" asChild>
                  <a href={account.profileUrl} target="_blank" rel="noopener noreferrer">
                    View Profile
                  </a>
                </Button>
                {account.status === 'connected' ? (
                  <Button variant="destructive" size="sm">Disconnect</Button>
                ) : (
                  <Button size="sm" onClick={handleConnectInstagram}>Reconnect</Button>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
