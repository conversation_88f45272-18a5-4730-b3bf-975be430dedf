"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Spa<PERSON>les, 
  Settings, 
  Target, 
  Palette,
  MessageSquare,
  Image as ImageIcon,
  Video,
  LayoutGrid
} from "lucide-react"

interface Business {
  id: string
  businessName: string
  industry: string
  brandBrief?: string
  brandElements?: {
    colorPalette?: Array<{
      colorHex: string
      colorName: string
    }>
    seoKeywords?: Array<{
      keyword: string
      relevanceScore: number
    }>
  }
}

interface GenerationSettings {
  platforms: string[]
  contentTypes: string[]
  tone: string
  focusAreas: string[]
  customPrompt?: string
}

interface GenerationControlsProps {
  business: Business
  onGenerate: (settings: GenerationSettings) => void
  isGenerating: boolean
}

const platformOptions = [
  { value: "instagram", label: "Instagram", icon: "📷" },
  { value: "facebook", label: "Facebook", icon: "👥" },
  { value: "twitter", label: "Twitter", icon: "🐦" },
  { value: "linkedin", label: "LinkedIn", icon: "💼" }
]

const contentTypeOptions = [
  { value: "image", label: "Single Image", icon: ImageIcon },
  { value: "carousel", label: "Carousel", icon: LayoutGrid },
  { value: "video", label: "Video", icon: Video },
  { value: "story", label: "Story", icon: MessageSquare }
]

const toneOptions = [
  { value: "professional", label: "Professional" },
  { value: "casual", label: "Casual & Friendly" },
  { value: "energetic", label: "Energetic & Bold" },
  { value: "educational", label: "Educational" },
  { value: "inspirational", label: "Inspirational" }
]

const focusAreaOptions = [
  { value: "product_features", label: "Product Features" },
  { value: "customer_stories", label: "Customer Stories" },
  { value: "behind_scenes", label: "Behind the Scenes" },
  { value: "industry_insights", label: "Industry Insights" },
  { value: "tips_advice", label: "Tips & Advice" },
  { value: "company_culture", label: "Company Culture" },
  { value: "testimonials", label: "Testimonials" },
  { value: "announcements", label: "Announcements" }
]

export default function GenerationControls({ 
  business, 
  onGenerate, 
  isGenerating 
}: GenerationControlsProps) {
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["instagram"])
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(["image"])
  const [selectedTone, setSelectedTone] = useState<string>("professional")
  const [selectedFocusAreas, setSelectedFocusAreas] = useState<string[]>(["product_features", "customer_stories"])
  const [customPrompt, setCustomPrompt] = useState<string>("")

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platform) 
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    )
  }

  const handleContentTypeToggle = (contentType: string) => {
    setSelectedContentTypes(prev => 
      prev.includes(contentType) 
        ? prev.filter(ct => ct !== contentType)
        : [...prev, contentType]
    )
  }

  const handleFocusAreaToggle = (focusArea: string) => {
    setSelectedFocusAreas(prev => 
      prev.includes(focusArea) 
        ? prev.filter(fa => fa !== focusArea)
        : [...prev, focusArea]
    )
  }

  const handleGenerate = () => {
    const settings: GenerationSettings = {
      platforms: selectedPlatforms,
      contentTypes: selectedContentTypes,
      tone: selectedTone,
      focusAreas: selectedFocusAreas,
      customPrompt: customPrompt.trim() || undefined
    }
    onGenerate(settings)
  }

  return (
    <div className="space-y-6">
      {/* Business Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Business Profile
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg">{business.businessName}</h3>
            <Badge variant="secondary" className="capitalize mt-1">
              {business.industry.replace('_', ' ')}
            </Badge>
          </div>

          {business.brandBrief && (
            <div>
              <Label className="text-sm font-medium">Brand Brief</Label>
              <p className="text-sm text-gray-600 mt-1">{business.brandBrief}</p>
            </div>
          )}

          {business.brandElements?.colorPalette && (
            <div>
              <Label className="text-sm font-medium flex items-center gap-2">
                <Palette className="h-4 w-4" />
                Brand Colors
              </Label>
              <div className="flex gap-2 mt-2">
                {business.brandElements.colorPalette.map((color, index) => (
                  <div
                    key={index}
                    className="flex flex-col items-center gap-1"
                  >
                    <div
                      className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                      style={{ backgroundColor: color.colorHex }}
                    />
                    <span className="text-xs text-gray-500">{color.colorName}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {business.brandElements?.seoKeywords && (
            <div>
              <Label className="text-sm font-medium">Key Topics</Label>
              <div className="flex flex-wrap gap-1 mt-2">
                {business.brandElements.seoKeywords.slice(0, 5).map((keyword, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {keyword.keyword}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Generation Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Content Generation Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Platforms */}
          <div>
            <Label className="text-sm font-medium">Target Platforms</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {platformOptions.map((platform) => (
                <Button
                  key={platform.value}
                  variant={selectedPlatforms.includes(platform.value) ? "default" : "outline"}
                  onClick={() => handlePlatformToggle(platform.value)}
                  className="justify-start"
                >
                  <span className="mr-2">{platform.icon}</span>
                  {platform.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Content Types */}
          <div>
            <Label className="text-sm font-medium">Content Types</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {contentTypeOptions.map((contentType) => {
                const Icon = contentType.icon
                return (
                  <Button
                    key={contentType.value}
                    variant={selectedContentTypes.includes(contentType.value) ? "default" : "outline"}
                    onClick={() => handleContentTypeToggle(contentType.value)}
                    className="justify-start"
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {contentType.label}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Tone */}
          <div>
            <Label className="text-sm font-medium">Content Tone</Label>
            <Select value={selectedTone} onValueChange={setSelectedTone}>
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Select tone" />
              </SelectTrigger>
              <SelectContent>
                {toneOptions.map((tone) => (
                  <SelectItem key={tone.value} value={tone.value}>
                    {tone.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Focus Areas */}
          <div>
            <Label className="text-sm font-medium">Content Focus Areas</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {focusAreaOptions.map((focusArea) => (
                <Button
                  key={focusArea.value}
                  variant={selectedFocusAreas.includes(focusArea.value) ? "default" : "outline"}
                  onClick={() => handleFocusAreaToggle(focusArea.value)}
                  size="sm"
                  className="justify-start text-xs"
                >
                  {focusArea.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Custom Prompt */}
          <div>
            <Label htmlFor="custom-prompt" className="text-sm font-medium">
              Additional Instructions (Optional)
            </Label>
            <Textarea
              id="custom-prompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Add any specific requirements or themes you'd like to include..."
              className="mt-2"
              rows={3}
            />
          </div>

          {/* Generate Button */}
          <Button 
            onClick={handleGenerate}
            disabled={isGenerating || selectedPlatforms.length === 0 || selectedContentTypes.length === 0}
            className="w-full"
            size="lg"
          >
            <Sparkles className="h-5 w-5 mr-2" />
            {isGenerating ? "Generating Content..." : "Generate 9 Posts"}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
