"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Building2, Palette, Target, Sparkles } from "lucide-react"

interface Business {
  id: string
  businessName: string
  industry: string
  brandBrief?: string
  brandElements?: {
    colorPalette?: Array<{
      colorHex: string
      colorName: string
    }>
    seoKeywords?: Array<{
      keyword: string
      relevanceScore: number
    }>
  }
}

interface BusinessSelectorProps {
  onBusinessSelect: (business: Business) => void
  selectedBusinessId?: string
}

// Dummy data for demonstration
const dummyBusinesses: Business[] = [
  {
    id: "1",
    businessName: "TechFlow Solutions",
    industry: "technology",
    brandBrief: "Innovative software solutions for modern businesses. We focus on automation, efficiency, and cutting-edge technology to help companies scale.",
    brandElements: {
      colorPalette: [
        { colorHex: "#3B82F6", colorName: "Primary Blue" },
        { colorHex: "#1E40AF", colorName: "Deep Blue" },
        { colorHex: "#F3F4F6", colorName: "Light Gray" }
      ],
      seoKeywords: [
        { keyword: "software development", relevanceScore: 95 },
        { keyword: "automation", relevanceScore: 88 },
        { keyword: "business solutions", relevanceScore: 82 }
      ]
    }
  },
  {
    id: "2", 
    businessName: "Green Garden Cafe",
    industry: "food_beverage",
    brandBrief: "Organic, locally-sourced cafe serving fresh, healthy meals. We believe in sustainability and community connection through food.",
    brandElements: {
      colorPalette: [
        { colorHex: "#10B981", colorName: "Forest Green" },
        { colorHex: "#F59E0B", colorName: "Golden Yellow" },
        { colorHex: "#8B5CF6", colorName: "Lavender" }
      ],
      seoKeywords: [
        { keyword: "organic food", relevanceScore: 92 },
        { keyword: "local cafe", relevanceScore: 87 },
        { keyword: "healthy meals", relevanceScore: 85 }
      ]
    }
  },
  {
    id: "3",
    businessName: "Urban Fitness Studio", 
    industry: "healthcare",
    brandBrief: "High-energy fitness studio offering personalized training and group classes. We empower people to achieve their fitness goals in a supportive community.",
    brandElements: {
      colorPalette: [
        { colorHex: "#EF4444", colorName: "Energy Red" },
        { colorHex: "#1F2937", colorName: "Charcoal" },
        { colorHex: "#FFFFFF", colorName: "Pure White" }
      ],
      seoKeywords: [
        { keyword: "fitness training", relevanceScore: 94 },
        { keyword: "group classes", relevanceScore: 89 },
        { keyword: "personal trainer", relevanceScore: 86 }
      ]
    }
  }
]

export default function BusinessSelector({ onBusinessSelect, selectedBusinessId }: BusinessSelectorProps) {
  const [businesses, setBusinesses] = useState<Business[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call with dummy data
    const fetchBusinesses = async () => {
      setLoading(true)
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      setBusinesses(dummyBusinesses)
      setLoading(false)
    }

    fetchBusinesses()
  }, [])

  if (loading) {
    return (
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">Select Your Business</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-2">Select Your Business</h2>
        <p className="text-gray-600">Choose a business to generate AI-powered social media content</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {businesses.map((business) => (
          <Card 
            key={business.id} 
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedBusinessId === business.id 
                ? 'ring-2 ring-blue-500 shadow-lg' 
                : 'hover:shadow-md'
            }`}
            onClick={() => onBusinessSelect(business)}
          >
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                {business.businessName}
              </CardTitle>
              <CardDescription>
                <Badge variant="secondary" className="capitalize">
                  {business.industry.replace('_', ' ')}
                </Badge>
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {business.brandBrief && (
                <p className="text-sm text-gray-600 line-clamp-3">
                  {business.brandBrief}
                </p>
              )}

              {business.brandElements?.colorPalette && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Palette className="h-4 w-4" />
                    <span className="text-sm font-medium">Brand Colors</span>
                  </div>
                  <div className="flex gap-2">
                    {business.brandElements.colorPalette.slice(0, 3).map((color, index) => (
                      <div
                        key={index}
                        className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                        style={{ backgroundColor: color.colorHex }}
                        title={color.colorName}
                      />
                    ))}
                  </div>
                </div>
              )}

              {business.brandElements?.seoKeywords && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <Target className="h-4 w-4" />
                    <span className="text-sm font-medium">Key Topics</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {business.brandElements.seoKeywords.slice(0, 2).map((keyword, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {keyword.keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <Button 
                className="w-full" 
                variant={selectedBusinessId === business.id ? "default" : "outline"}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                {selectedBusinessId === business.id ? "Selected" : "Generate Content"}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
