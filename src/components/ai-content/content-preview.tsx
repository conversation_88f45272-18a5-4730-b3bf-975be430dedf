"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { 
  Heart, 
  MessageCircle, 
  Share, 
  Edit3, 
  Check, 
  X, 
  Instagram,
  Facebook,
  Twitter,
  Linkedin
} from "lucide-react"

interface GeneratedPost {
  id: string
  title: string
  caption: string
  hashtags: string[]
  platform: string
  contentType: string
  imageUrl: string
  visualDescription: string
  businessName: string
}

interface ContentPreviewProps {
  post: GeneratedPost
  onEdit: (postId: string, updatedPost: Partial<GeneratedPost>) => void
  onToggleSelect: (postId: string) => void
  isSelected: boolean
  isEditing?: boolean
}

const platformIcons = {
  instagram: Instagram,
  facebook: Facebook,
  twitter: Twitter,
  linkedin: Linkedin
}

const platformColors = {
  instagram: "bg-gradient-to-r from-purple-500 to-pink-500",
  facebook: "bg-blue-600",
  twitter: "bg-sky-500", 
  linkedin: "bg-blue-700"
}

export default function ContentPreview({ 
  post, 
  onEdit, 
  onToggleSelect, 
  isSelected,
  isEditing = false 
}: ContentPreviewProps) {
  const [editMode, setEditMode] = useState(isEditing)
  const [editedCaption, setEditedCaption] = useState(post.caption)
  const [editedHashtags, setEditedHashtags] = useState(post.hashtags.join(' '))

  const PlatformIcon = platformIcons[post.platform as keyof typeof platformIcons] || Instagram

  const handleSaveEdit = () => {
    onEdit(post.id, {
      caption: editedCaption,
      hashtags: editedHashtags.split(' ').filter(tag => tag.trim() !== '')
    })
    setEditMode(false)
  }

  const handleCancelEdit = () => {
    setEditedCaption(post.caption)
    setEditedHashtags(post.hashtags.join(' '))
    setEditMode(false)
  }

  return (
    <Card className={`transition-all duration-200 ${isSelected ? 'ring-2 ring-blue-500 shadow-lg' : 'hover:shadow-md'}`}>
      <CardContent className="p-0">
        {/* Platform Header */}
        <div className={`${platformColors[post.platform as keyof typeof platformColors]} text-white p-3 flex items-center justify-between`}>
          <div className="flex items-center gap-2">
            <PlatformIcon className="h-4 w-4" />
            <span className="text-sm font-medium capitalize">{post.platform}</span>
          </div>
          <Badge variant="secondary" className="text-xs">
            {post.contentType}
          </Badge>
        </div>

        {/* Post Image */}
        <div className="relative">
          <img 
            src={post.imageUrl} 
            alt={post.visualDescription}
            className="w-full h-48 object-cover"
          />
          {/* Selection Overlay */}
          <div 
            className={`absolute inset-0 bg-blue-500 bg-opacity-20 flex items-center justify-center cursor-pointer transition-opacity ${
              isSelected ? 'opacity-100' : 'opacity-0 hover:opacity-100'
            }`}
            onClick={() => onToggleSelect(post.id)}
          >
            <div className={`w-8 h-8 rounded-full border-2 border-white flex items-center justify-center ${
              isSelected ? 'bg-blue-500' : 'bg-transparent'
            }`}>
              {isSelected && <Check className="h-4 w-4 text-white" />}
            </div>
          </div>
        </div>

        {/* Post Content */}
        <div className="p-4 space-y-3">
          {/* Business Profile */}
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
              <span className="text-xs font-semibold">
                {post.businessName.charAt(0)}
              </span>
            </div>
            <span className="text-sm font-medium">{post.businessName}</span>
          </div>

          {/* Caption */}
          {editMode ? (
            <div className="space-y-2">
              <Textarea
                value={editedCaption}
                onChange={(e) => setEditedCaption(e.target.value)}
                className="min-h-[80px] text-sm"
                placeholder="Edit caption..."
              />
              <Textarea
                value={editedHashtags}
                onChange={(e) => setEditedHashtags(e.target.value)}
                className="min-h-[60px] text-sm"
                placeholder="Edit hashtags..."
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={handleSaveEdit}>
                  <Check className="h-3 w-3 mr-1" />
                  Save
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                  <X className="h-3 w-3 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <p className="text-sm text-gray-800 line-clamp-3">{post.caption}</p>
              {post.hashtags.length > 0 && (
                <p className="text-sm text-blue-600">
                  {post.hashtags.map(tag => tag.startsWith('#') ? tag : `#${tag}`).join(' ')}
                </p>
              )}
            </div>
          )}

          {/* Engagement Simulation */}
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center gap-4 text-gray-500">
              <div className="flex items-center gap-1">
                <Heart className="h-4 w-4" />
                <span className="text-xs">{Math.floor(Math.random() * 100) + 20}</span>
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="h-4 w-4" />
                <span className="text-xs">{Math.floor(Math.random() * 20) + 5}</span>
              </div>
              <div className="flex items-center gap-1">
                <Share className="h-4 w-4" />
                <span className="text-xs">{Math.floor(Math.random() * 10) + 2}</span>
              </div>
            </div>
            
            {!editMode && (
              <Button 
                size="sm" 
                variant="ghost" 
                onClick={() => setEditMode(true)}
                className="h-8 w-8 p-0"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
