"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import ContentPreview from "./content-preview"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { 
  RefreshCw, 
  Save, 
  CheckSquare, 
  Square, 
  Sparkles,
  Download
} from "lucide-react"

interface GeneratedPost {
  id: string
  title: string
  caption: string
  hashtags: string[]
  platform: string
  contentType: string
  imageUrl: string
  visualDescription: string
  businessName: string
}

interface ContentGridProps {
  posts: GeneratedPost[]
  isGenerating: boolean
  onRegenerate: () => void
  onRegeneratePost: (postId: string) => void
  onEditPost: (postId: string, updatedPost: Partial<GeneratedPost>) => void
  onSaveAll: (selectedPosts: GeneratedPost[]) => void
  businessName: string
}

// Dummy generated posts for demonstration
const dummyPosts: GeneratedPost[] = [
  {
    id: "1",
    title: "Tech Innovation Spotlight",
    caption: "🚀 Revolutionizing the way businesses operate with cutting-edge automation solutions. Our latest AI-powered tools are helping companies save 40% more time on routine tasks. Ready to transform your workflow?",
    hashtags: ["#TechInnovation", "#Automation", "#BusinessSolutions", "#AI", "#Productivity"],
    platform: "instagram",
    contentType: "image",
    imageUrl: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400&h=400&fit=crop",
    visualDescription: "Modern office setup with multiple monitors displaying code and analytics",
    businessName: "TechFlow Solutions"
  },
  {
    id: "2", 
    title: "Software Development Excellence",
    caption: "💻 Behind every great business is great software. Our development team crafts custom solutions that scale with your growth. From concept to deployment, we've got you covered.",
    hashtags: ["#SoftwareDevelopment", "#CustomSolutions", "#TechTeam", "#Innovation"],
    platform: "linkedin",
    contentType: "image",
    imageUrl: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=400&fit=crop",
    visualDescription: "Clean code on a dark theme IDE with multiple programming languages",
    businessName: "TechFlow Solutions"
  },
  {
    id: "3",
    title: "Client Success Story",
    caption: "🎯 Another successful project delivery! We helped a local startup automate their inventory management, resulting in 60% faster processing times. Success stories like these fuel our passion for innovation.",
    hashtags: ["#ClientSuccess", "#CaseStudy", "#Automation", "#Results"],
    platform: "facebook",
    contentType: "carousel",
    imageUrl: "https://images.unsplash.com/photo-1552664730-d307ca884978?w=400&h=400&fit=crop",
    visualDescription: "Team celebrating project completion with charts showing improved metrics",
    businessName: "TechFlow Solutions"
  },
  {
    id: "4",
    title: "Tech Tips Tuesday",
    caption: "💡 Tech Tip: Did you know that proper API documentation can reduce development time by up to 50%? Here are our top 3 tips for writing better API docs that your team will actually use.",
    hashtags: ["#TechTips", "#API", "#Documentation", "#BestPractices", "#Development"],
    platform: "twitter",
    contentType: "image",
    imageUrl: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=400&h=400&fit=crop",
    visualDescription: "Clean API documentation interface with code examples",
    businessName: "TechFlow Solutions"
  },
  {
    id: "5",
    title: "Team Spotlight",
    caption: "👥 Meet our amazing development team! With over 50 years of combined experience, they're the driving force behind our innovative solutions. Passionate about code, committed to excellence.",
    hashtags: ["#TeamSpotlight", "#Developers", "#TechTeam", "#Innovation", "#Excellence"],
    platform: "instagram",
    contentType: "carousel",
    imageUrl: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=400&h=400&fit=crop",
    visualDescription: "Professional team photo in modern office environment",
    businessName: "TechFlow Solutions"
  },
  {
    id: "6",
    title: "Industry Insights",
    caption: "📊 The future of business automation is here. Recent studies show that companies using AI-driven automation see 3x faster growth rates. Are you ready to join the revolution?",
    hashtags: ["#IndustryInsights", "#BusinessAutomation", "#Growth", "#Future", "#AI"],
    platform: "linkedin",
    contentType: "image",
    imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=400&fit=crop",
    visualDescription: "Futuristic dashboard with AI analytics and growth charts",
    businessName: "TechFlow Solutions"
  },
  {
    id: "7",
    title: "Product Feature Highlight",
    caption: "⚡ New Feature Alert! Our latest update includes real-time collaboration tools that make remote development seamless. Try it today and experience the difference.",
    hashtags: ["#NewFeature", "#Collaboration", "#RemoteWork", "#ProductUpdate"],
    platform: "twitter",
    contentType: "video",
    imageUrl: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=400&fit=crop",
    visualDescription: "Screen recording showing new collaboration features in action",
    businessName: "TechFlow Solutions"
  },
  {
    id: "8",
    title: "Customer Testimonial",
    caption: "⭐ 'TechFlow Solutions transformed our business operations completely. Their automation tools saved us countless hours and improved our efficiency dramatically.' - Sarah, CEO of GrowthCorp",
    hashtags: ["#CustomerTestimonial", "#Success", "#Efficiency", "#Transformation"],
    platform: "facebook",
    contentType: "image",
    imageUrl: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&h=400&fit=crop",
    visualDescription: "Professional headshot of satisfied customer with quote overlay",
    businessName: "TechFlow Solutions"
  },
  {
    id: "9",
    title: "Behind the Scenes",
    caption: "🔧 Ever wondered how we build our solutions? Take a peek behind the scenes at our development process. From ideation to deployment, every step is carefully crafted for excellence.",
    hashtags: ["#BehindTheScenes", "#Development", "#Process", "#Quality", "#Excellence"],
    platform: "instagram",
    contentType: "story",
    imageUrl: "https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?w=400&h=400&fit=crop",
    visualDescription: "Time-lapse style image showing development workflow and tools",
    businessName: "TechFlow Solutions"
  }
]

export default function ContentGrid({
  posts = dummyPosts,
  isGenerating,
  onRegenerate,
  onRegeneratePost,
  onEditPost,
  onSaveAll,
  businessName
}: ContentGridProps) {
  const [selectedPosts, setSelectedPosts] = useState<Set<string>>(new Set())

  const handleToggleSelect = (postId: string) => {
    const newSelected = new Set(selectedPosts)
    if (newSelected.has(postId)) {
      newSelected.delete(postId)
    } else {
      newSelected.add(postId)
    }
    setSelectedPosts(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedPosts.size === posts.length) {
      setSelectedPosts(new Set())
    } else {
      setSelectedPosts(new Set(posts.map(p => p.id)))
    }
  }

  const handleSaveSelected = () => {
    const selectedPostsData = posts.filter(post => selectedPosts.has(post.id))
    onSaveAll(selectedPostsData)
  }

  if (isGenerating) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Generating AI Content for {businessName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <LoadingSpinner size="lg" />
            <p className="text-gray-600">Creating 9 unique social media posts...</p>
            <p className="text-sm text-gray-500">This may take a few moments</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                Generated Content for {businessName}
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {posts.length} posts generated • {selectedPosts.size} selected
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                {selectedPosts.size}/{posts.length} selected
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button
              variant="outline"
              onClick={handleSelectAll}
              className="flex items-center gap-2"
            >
              {selectedPosts.size === posts.length ? (
                <CheckSquare className="h-4 w-4" />
              ) : (
                <Square className="h-4 w-4" />
              )}
              {selectedPosts.size === posts.length ? "Deselect All" : "Select All"}
            </Button>
            
            <Button
              variant="outline"
              onClick={onRegenerate}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Regenerate All
            </Button>

            <Button
              onClick={handleSaveSelected}
              disabled={selectedPosts.size === 0}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Save Selected ({selectedPosts.size})
            </Button>

            <Button
              variant="outline"
              disabled={selectedPosts.size === 0}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export Selected
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post) => (
          <ContentPreview
            key={post.id}
            post={post}
            onEdit={onEditPost}
            onToggleSelect={handleToggleSelect}
            isSelected={selectedPosts.has(post.id)}
          />
        ))}
      </div>
    </div>
  )
}
