{"name": "dukanify", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "create-mock-user": "cross-env NODE_OPTIONS=--no-deprecation ts-node src/scripts/create-mock-user.ts", "dev:server": "cross-env NODE_OPTIONS=--no-deprecation ENABLE_SCHEDULER=true ts-node src/server.ts", "build:server": "tsc --project tsconfig.server.json", "start:server": "cross-env NODE_OPTIONS=--no-deprecation ENABLE_SCHEDULER=true node dist/server.js", "process-scheduled-posts": "cross-env NODE_OPTIONS=--no-deprecation ts-node src/scripts/process-scheduled-posts.ts"}, "dependencies": {"@payloadcms/db-mongodb": "3.33.0", "@payloadcms/next": "3.33.0", "@payloadcms/payload-cloud": "3.33.0", "@payloadcms/richtext-lexical": "3.33.0", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "aws4": "^1.13.2", "axios": "^1.6.7", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "express": "^4.18.3", "form-data": "^4.0.0", "graphql": "^16.8.1", "next": "15.3.0", "node-cron": "^3.0.3", "payload": "3.33.0", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "0.32.6"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@radix-ui/react-icons": "^1.3.2", "@tailwindcss/postcss": "^4.1.6", "@types/express": "^4.17.21", "@types/node": "^22.5.4", "@types/node-cron": "^3.0.11", "@types/react": "19.1.0", "@types/react-dom": "19.1.2", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.16.0", "eslint-config-next": "15.3.0", "lucide-react": "^0.510.0", "postcss": "^8.5.3", "prettier": "^3.4.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.6", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "5.7.3"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}, "pnpm": {"onlyBuiltDependencies": ["sharp"]}}